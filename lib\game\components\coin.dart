import 'dart:math';
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../arza_rush_game.dart';

class Coin extends CircleComponent with HasGameRef<ArzaRushGame> {
  
  // Coin properties
  static const double coinRadius = 15.0;
  
  // Movement and animation
  double _speed = 0.0;
  double _rotationSpeed = 5.0;
  double _bobOffset = 0.0;
  double _bobSpeed = 3.0;
  double _bobAmplitude = 5.0;
  
  // Visual properties
  late Timer _sparkleTimer;
  bool _isSparkleVisible = false;
  final Random _random = Random();
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Set coin size
    radius = coinRadius;
    
    // Set golden color
    paint.color = Colors.amber;
    
    // Add collision detection
    add(CircleHitbox());
    
    // Set initial speed
    _speed = gameRef.gameSpeed;
    
    // Setup sparkle animation
    _sparkleTimer = Timer(
      0.5 + _random.nextDouble() * 0.5, // Random sparkle interval
      repeat: true,
      onTick: () {
        _isSparkleVisible = !_isSparkleVisible;
      },
    );
    _sparkleTimer.start();
    
    // Random initial bob offset
    _bobOffset = _random.nextDouble() * 2 * pi;
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Update timers
    _sparkleTimer.update(dt);
    
    // Update speed to match game speed
    _speed = gameRef.gameSpeed;
    
    // Move coin to the left
    position.x -= _speed * dt;
    
    // Add rotation animation
    angle += _rotationSpeed * dt;
    
    // Add bobbing animation
    _bobOffset += _bobSpeed * dt;
    final bobY = sin(_bobOffset) * _bobAmplitude;
    position.y += bobY * dt;
    
    // Remove coin when it goes off screen
    if (position.x + radius < 0) {
      removeFromParent();
    }
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw the main coin body
    final coinPaint = Paint()
      ..color = Colors.amber
      ..style = PaintingStyle.fill;
    canvas.drawCircle(Offset.zero, radius, coinPaint);
    
    // Draw coin border
    final borderPaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawCircle(Offset.zero, radius, borderPaint);
    
    // Draw inner circle
    final innerPaint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    canvas.drawCircle(Offset.zero, radius * 0.7, innerPaint);
    
    // Draw dollar sign or star symbol
    final symbolPaint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    
    // Draw a star symbol
    _drawStar(canvas, symbolPaint);
    
    // Draw sparkles if visible
    if (_isSparkleVisible) {
      _drawSparkles(canvas);
    }
  }
  
  void _drawStar(Canvas canvas, Paint paint) {
    final path = Path();
    final centerX = 0.0;
    final centerY = 0.0;
    final outerRadius = radius * 0.4;
    final innerRadius = radius * 0.2;
    
    for (int i = 0; i < 10; i++) {
      final angle = (i * pi) / 5;
      final radius = i.isEven ? outerRadius : innerRadius;
      final x = centerX + radius * cos(angle);
      final y = centerY + radius * sin(angle);
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    
    canvas.drawPath(path, paint);
  }
  
  void _drawSparkles(Canvas canvas) {
    final sparklePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2;
    
    // Draw sparkles around the coin
    final sparklePositions = [
      Offset(-radius * 1.2, -radius * 0.8),
      Offset(radius * 1.2, -radius * 0.8),
      Offset(-radius * 1.2, radius * 0.8),
      Offset(radius * 1.2, radius * 0.8),
    ];
    
    for (final pos in sparklePositions) {
      // Draw a small cross for sparkle effect
      canvas.drawLine(
        Offset(pos.dx - 3, pos.dy),
        Offset(pos.dx + 3, pos.dy),
        sparklePaint,
      );
      canvas.drawLine(
        Offset(pos.dx, pos.dy - 3),
        Offset(pos.dx, pos.dy + 3),
        sparklePaint,
      );
    }
  }
  
  @override
  void onRemove() {
    _sparkleTimer.stop();
    super.onRemove();
  }
}
