import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/collisions.dart';
import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;
import 'main.dart';
import 'game_components.dart';

// شاشة اللعبة الرئيسية
class GameScreen extends StatefulWidget {
  final int levelIndex;

  const GameScreen({super.key, required this.levelIndex});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late ArzaFlowGame game;

  @override
  void initState() {
    super.initState();
    game = ArzaFlowGame(levelIndex: widget.levelIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB),
              Color(0xFF4682B4),
              Color(0xFF1E90FF),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // شريط المعلومات العلوي
              Container(
                padding: const EdgeInsets.all(15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر الرجوع
                    IconButton(
                      onPressed: () {
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(
                            builder: (context) => const MainMenuScreen(),
                          ),
                        );
                      },
                      icon: const Icon(
                        Icons.arrow_back_rounded,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),

                    // معلومات المستوى
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.blue.withOpacity(0.8),
                            Colors.cyan.withOpacity(0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        'Level ${widget.levelIndex + 1}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),

                    // زر الإعدادات
                    IconButton(
                      onPressed: () => _showPauseMenu(),
                      icon: const Icon(
                        Icons.pause_rounded,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                  ],
                ),
              ),

              // منطقة اللعبة
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(18),
                    child: GameWidget<ArzaFlowGame>.controlled(
                      gameFactory: () => game,
                    ),
                  ),
                ),
              ),

              // شريط الأدوات السفلي
              Container(
                padding: const EdgeInsets.all(15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildToolButton(
                      'Reset',
                      Icons.refresh_rounded,
                      Colors.orange,
                      () => game.resetLevel(),
                    ),
                    _buildToolButton(
                      'Hint',
                      Icons.lightbulb_rounded,
                      Colors.yellow,
                      () => game.showHint(),
                    ),
                    _buildToolButton(
                      'Skip',
                      Icons.skip_next_rounded,
                      Colors.purple,
                      () => _skipLevel(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildToolButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                color.withOpacity(0.8),
                color,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.4),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(30),
              onTap: () {
                HapticFeedback.lightImpact();
                SoundManager.playSound('click');
                onPressed();
              },
              child: Icon(
                icon,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        ),
        const SizedBox(height: 5),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  void _showPauseMenu() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Game Paused',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.play_arrow_rounded),
                title: const Text('Resume'),
                onTap: () => Navigator.of(context).pop(),
              ),
              ListTile(
                leading: const Icon(Icons.refresh_rounded),
                title: const Text('Restart Level'),
                onTap: () {
                  Navigator.of(context).pop();
                  game.resetLevel();
                },
              ),
              ListTile(
                leading: const Icon(Icons.home_rounded),
                title: const Text('Main Menu'),
                onTap: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const MainMenuScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _skipLevel() {
    if (widget.levelIndex < 19) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => GameScreen(levelIndex: widget.levelIndex + 1),
        ),
      );
    } else {
      // آخر مستوى - العودة للقائمة الرئيسية
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const MainMenuScreen(),
        ),
      );
    }
  }
}


