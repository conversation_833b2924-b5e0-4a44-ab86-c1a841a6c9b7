import 'package:flutter/material.dart';
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flame/game.dart';
import 'dart:math';
import 'dart:ui' as ui;
import 'main.dart';

// تعريف مسبق لكلاس ArzaFlowGame
class ArzaFlowGame extends FlameGame with HasCollisionDetection {
  final int levelIndex;
  late List<WaterParticle> waterParticles;
  late WaterSource waterSource;
  late WaterTarget waterTarget;
  late List<Obstacle> obstacles;
  late List<Tool> tools;

  bool isLevelComplete = false;
  bool isGamePaused = false;

  ArzaFlowGame({required this.levelIndex}) {
    waterParticles = [];
    obstacles = [];
    tools = [];
  }

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // تهيئة المستوى
    _initializeLevel();
  }

  void _initializeLevel() {
    // مسح العناصر السابقة
    removeAll(children);
    waterParticles = [];
    obstacles = [];
    tools = [];
    isLevelComplete = false;

    // إنشاء خلفية المستوى
    add(LevelBackground(levelIndex: levelIndex));

    // إنشاء مصدر الماء
    waterSource = WaterSource(
      position: Vector2(size.x * 0.1, size.y * 0.2),
    );
    add(waterSource);

    // إنشاء هدف الماء
    waterTarget = WaterTarget(
      position: Vector2(size.x * 0.8, size.y * 0.8),
    );
    add(waterTarget);

    // إنشاء العوائق والأدوات حسب المستوى
    _createLevelElements();
  }

  void _createLevelElements() {
    switch (levelIndex) {
      case 0:
        _createLevel1();
        break;
      case 1:
        _createLevel2();
        break;
      default:
        _createRandomLevel();
    }
  }

  void _createLevel1() {
    // مستوى بسيط - حفر مباشر
    // لا توجد عوائق، فقط حفر بسيط
  }

  void _createLevel2() {
    // مستوى مع عائق واحد
    obstacles.add(
      Obstacle(
        position: Vector2(size.x * 0.5, size.y * 0.5),
        size: Vector2(100, 50),
      ),
    );
    add(obstacles.last);
  }

  void _createRandomLevel() {
    // مستوى عشوائي للمستويات المتقدمة
    final random = Random();
    for (int i = 0; i < 3; i++) {
      obstacles.add(
        Obstacle(
          position: Vector2(
            random.nextDouble() * size.x,
            random.nextDouble() * size.y,
          ),
          size: Vector2(80, 40),
        ),
      );
      add(obstacles.last);
    }
  }

  void resetLevel() {
    _initializeLevel();
  }

  void showHint() {
    // إظهار تلميح للمستوى
    SoundManager.playSound('hint');
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (isGamePaused || isLevelComplete) return;

    // تحديث جسيمات الماء
    _updateWaterPhysics(dt);

    // فحص إكمال المستوى
    _checkLevelCompletion();
  }

  void _updateWaterPhysics(double dt) {
    // محاكاة فيزياء الماء البسيطة
    for (final particle in waterParticles) {
      particle.update(dt);
    }

    // إزالة الجسيمات التي خرجت من الشاشة
    waterParticles.removeWhere((particle) =>
      particle.position.x < 0 ||
      particle.position.x > size.x ||
      particle.position.y > size.y
    );
  }

  void _checkLevelCompletion() {
    // فحص وصول الماء للهدف
    if (waterTarget.hasEnoughWater() && !isLevelComplete) {
      isLevelComplete = true;
      SoundManager.playSound('level_complete');
      _showLevelCompleteDialog();
    }
  }

  void _showLevelCompleteDialog() {
    // سيتم إضافة حوار إكمال المستوى لاحقاً
  }
}

// خلفية المستوى
class LevelBackground extends Component with HasGameReference {
  final int levelIndex;

  LevelBackground({required this.levelIndex});

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    // رسم خلفية متدرجة حسب المستوى
    final paint = Paint()
      ..shader = ui.Gradient.linear(
        const Offset(0, 0),
        Offset(0, game.size.y),
        _getLevelColors(),
      );

    canvas.drawRect(
      Rect.fromLTWH(0, 0, game.size.x, game.size.y),
      paint,
    );

    // رسم عناصر الخلفية
    _drawBackgroundElements(canvas);
  }

  List<Color> _getLevelColors() {
    switch (levelIndex % 5) {
      case 0:
        return [const Color(0xFF87CEEB), const Color(0xFF4682B4)]; // أزرق فاتح
      case 1:
        return [const Color(0xFF98FB98), const Color(0xFF32CD32)]; // أخضر
      case 2:
        return [const Color(0xFFFFB6C1), const Color(0xFFFF69B4)]; // وردي
      case 3:
        return [const Color(0xFFFFE4B5), const Color(0xFFDEB887)]; // بيج
      case 4:
        return [const Color(0xFFE6E6FA), const Color(0xFF9370DB)]; // بنفسجي
      default:
        return [const Color(0xFF87CEEB), const Color(0xFF4682B4)];
    }
  }

  void _drawBackgroundElements(Canvas canvas) {
    // رسم سحب بسيطة
    final cloudPaint = Paint()
      ..color = Colors.white.withOpacity(0.3);
    
    for (int i = 0; i < 3; i++) {
      final x = (i * 150.0) + 50;
      final y = 50.0 + (i * 30);
      
      canvas.drawCircle(Offset(x, y), 20, cloudPaint);
      canvas.drawCircle(Offset(x + 25, y), 25, cloudPaint);
      canvas.drawCircle(Offset(x + 50, y), 20, cloudPaint);
    }
  }
}

// مصدر الماء
class WaterSource extends PositionComponent with HasGameReference, CollisionCallbacks {
  late Timer _waterTimer;
  bool isActive = true;

  WaterSource({required Vector2 position}) : super(position: position, size: Vector2(60, 60));

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    add(RectangleHitbox());
    
    // إنتاج الماء كل 0.1 ثانية
    _waterTimer = Timer(
      0.1,
      repeat: true,
      onTick: _produceWater,
    );
    _waterTimer.start();
  }

  void _produceWater() {
    if (!isActive) return;
    
    // إنشاء جسيم ماء جديد
    final waterParticle = WaterParticle(
      position: position + Vector2(size.x / 2, size.y),
    );
    
    game.add(waterParticle);
    if (game is ArzaFlowGame) {
      (game as ArzaFlowGame).waterParticles.add(waterParticle);
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // رسم مصدر الماء
    final paint = Paint()
      ..shader = ui.Gradient.radial(
        Offset(size.x / 2, size.y / 2),
        size.x / 2,
        [
          const Color(0xFF00BFFF),
          const Color(0xFF1E90FF),
          const Color(0xFF0066CC),
        ],
      );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.x, size.y),
        const Radius.circular(10),
      ),
      paint,
    );
    
    // رسم رمز الماء
    final iconPaint = Paint()..color = Colors.white;
    canvas.drawCircle(
      Offset(size.x / 2, size.y / 2 - 5),
      8,
      iconPaint,
    );
    canvas.drawCircle(
      Offset(size.x / 2 - 8, size.y / 2 + 5),
      6,
      iconPaint,
    );
    canvas.drawCircle(
      Offset(size.x / 2 + 8, size.y / 2 + 5),
      6,
      iconPaint,
    );
  }

  @override
  void update(double dt) {
    super.update(dt);
    _waterTimer.update(dt);
  }
}

// هدف الماء
class WaterTarget extends PositionComponent with HasGameReference, CollisionCallbacks {
  double waterLevel = 0.0;
  final double targetLevel = 100.0;

  WaterTarget({required Vector2 position}) : super(position: position, size: Vector2(80, 80));

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // رسم الخزان
    final tankPaint = Paint()
      ..color = Colors.brown.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.x, size.y),
        const Radius.circular(8),
      ),
      tankPaint,
    );
    
    // رسم مستوى الماء
    if (waterLevel > 0) {
      final waterHeight = (waterLevel / targetLevel) * size.y;
      final waterPaint = Paint()
        ..shader = ui.Gradient.linear(
          Offset(0, size.y - waterHeight),
          Offset(0, size.y),
          [
            const Color(0xFF00BFFF).withOpacity(0.8),
            const Color(0xFF1E90FF).withOpacity(0.8),
          ],
        );
      
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(2, size.y - waterHeight, size.x - 4, waterHeight - 2),
          const Radius.circular(6),
        ),
        waterPaint,
      );
    }
    
    // رسم شريط التقدم
    final progressPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawRect(
      Rect.fromLTWH(size.x + 10, 10, 20, size.y - 20),
      progressPaint,
    );
    
    final progressFillPaint = Paint()
      ..color = waterLevel >= targetLevel ? Colors.green : Colors.blue;
    
    final progressHeight = (waterLevel / targetLevel) * (size.y - 20);
    canvas.drawRect(
      Rect.fromLTWH(size.x + 12, size.y - 10 - progressHeight, 16, progressHeight),
      progressFillPaint,
    );
  }

  void addWater(double amount) {
    waterLevel = (waterLevel + amount).clamp(0.0, targetLevel);
    if (waterLevel >= targetLevel) {
      SoundManager.playSound('success');
    }
  }

  bool hasEnoughWater() {
    return waterLevel >= targetLevel;
  }
}

// جسيم الماء
class WaterParticle extends PositionComponent with HasGameReference, CollisionCallbacks {
  Vector2 velocity = Vector2.zero();
  final double gravity = 300.0;
  final double bounce = 0.3;
  bool isActive = true;

  WaterParticle({required Vector2 position}) : super(position: position, size: Vector2(8, 8));

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    add(CircleHitbox());
    
    // سرعة ابتدائية عشوائية
    velocity = Vector2(
      (Random().nextDouble() - 0.5) * 100,
      Random().nextDouble() * 50 + 50,
    );
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    if (!isActive) return;
    
    // تطبيق الجاذبية
    velocity.y += gravity * dt;
    
    // تحديث الموقع
    position += velocity * dt;
    
    // ارتداد من الحواف
    if (position.x <= 0 || position.x >= game.size.x - size.x) {
      velocity.x *= -bounce;
      position.x = position.x <= 0 ? 0 : game.size.x - size.x;
    }

    if (position.y >= game.size.y - size.y) {
      velocity.y *= -bounce;
      position.y = game.size.y - size.y;
      
      // إزالة الجسيم إذا توقف
      if (velocity.y.abs() < 10) {
        isActive = false;
        removeFromParent();
      }
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    final paint = Paint()
      ..shader = ui.Gradient.radial(
        Offset(size.x / 2, size.y / 2),
        size.x / 2,
        [
          const Color(0xFF00BFFF).withOpacity(0.9),
          const Color(0xFF1E90FF).withOpacity(0.7),
        ],
      );
    
    canvas.drawCircle(
      Offset(size.x / 2, size.y / 2),
      size.x / 2,
      paint,
    );
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is WaterTarget) {
      other.addWater(5.0);
      isActive = false;
      removeFromParent();
      return true;
    }
    return false;
  }
}

// عائق
class Obstacle extends PositionComponent with HasGameReference, CollisionCallbacks {
  Obstacle({required Vector2 position, required Vector2 size}) 
      : super(position: position, size: size);

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    final paint = Paint()
      ..color = Colors.brown.withOpacity(0.8);
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.x, size.y),
        const Radius.circular(5),
      ),
      paint,
    );
    
    // رسم نسيج الخشب
    final texturePaint = Paint()
      ..color = Colors.brown.withOpacity(0.3)
      ..strokeWidth = 2;
    
    for (int i = 0; i < size.y.toInt(); i += 10) {
      canvas.drawLine(
        Offset(0, i.toDouble()),
        Offset(size.x, i.toDouble()),
        texturePaint,
      );
    }
  }
}

// أداة (للاستخدام المستقبلي)
class Tool extends PositionComponent with HasGameReference, CollisionCallbacks {
  final String toolType;
  
  Tool({required Vector2 position, required this.toolType}) 
      : super(position: position, size: Vector2(40, 40));

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.8);
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.x, size.y),
        const Radius.circular(8),
      ),
      paint,
    );
  }
}
