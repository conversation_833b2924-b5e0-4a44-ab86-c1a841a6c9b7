import 'dart:math';
import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import '../arza_rush_game.dart';

class Background extends Component with HasGameRef<ArzaRushGame> {
  
  // Background layers
  late List<BackgroundLayer> _layers;
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Create multiple background layers for parallax effect
    _layers = [
      BackgroundLayer(
        color: const Color(0xFF87CEEB), // Sky blue
        speed: 0.1,
        isGround: false,
      ),
      BackgroundLayer(
        color: const Color(0xFF90EE90), // Light green (far hills)
        speed: 0.3,
        isGround: false,
      ),
      BackgroundLayer(
        color: const Color(0xFF228B22), // Forest green (near hills)
        speed: 0.5,
        isGround: false,
      ),
      BackgroundLayer(
        color: const Color(0xFF8B4513), // Brown (ground)
        speed: 1.0,
        isGround: true,
      ),
    ];
    
    for (final layer in _layers) {
      add(layer);
    }
  }
}

class BackgroundLayer extends RectangleComponent with <PERSON><PERSON>ame<PERSON>ef<ArzaRushGame> {
  final Color color;
  final double speed;
  final bool isGround;
  
  // Scrolling properties
  double _scrollOffset = 0.0;
  late List<Vector2> _cloudPositions;
  late List<Vector2> _treePositions;
  final Random _random = Random();
  
  BackgroundLayer({
    required this.color,
    required this.speed,
    required this.isGround,
  });
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Set layer size to cover the entire screen
    size = gameRef.size;
    paint.color = color;
    
    // Generate random positions for decorative elements
    if (!isGround) {
      _generateClouds();
    } else {
      _generateTrees();
    }
  }
  
  void _generateClouds() {
    _cloudPositions = [];
    for (int i = 0; i < 5; i++) {
      _cloudPositions.add(Vector2(
        _random.nextDouble() * size.x * 2,
        50 + _random.nextDouble() * 100,
      ));
    }
  }
  
  void _generateTrees() {
    _treePositions = [];
    for (int i = 0; i < 8; i++) {
      _treePositions.add(Vector2(
        _random.nextDouble() * size.x * 2,
        size.y - 150 - _random.nextDouble() * 50,
      ));
    }
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Update scroll offset based on game speed and layer speed
    _scrollOffset += gameRef.gameSpeed * speed * dt;
    
    // Reset scroll offset to prevent overflow
    if (_scrollOffset > size.x) {
      _scrollOffset -= size.x;
    }
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    if (isGround) {
      _renderGround(canvas);
    } else {
      _renderSky(canvas);
    }
  }
  
  void _renderSky(Canvas canvas) {
    // Draw clouds
    final cloudPaint = Paint()
      ..color = Colors.white.withOpacity(0.8);
    
    for (final cloudPos in _cloudPositions) {
      final x = (cloudPos.x - _scrollOffset) % (size.x + 100) - 50;
      _drawCloud(canvas, Offset(x, cloudPos.y), cloudPaint);
    }
  }
  
  void _renderGround(Canvas canvas) {
    // Draw ground texture
    final grassPaint = Paint()
      ..color = Colors.green;
    
    // Draw grass blades
    for (int i = 0; i < size.x.toInt(); i += 10) {
      final x = (i - _scrollOffset) % size.x;
      _drawGrass(canvas, Offset(x, size.y - 150), grassPaint);
    }
    
    // Draw trees
    final treePaint = Paint()
      ..color = Colors.brown;
    final leafPaint = Paint()
      ..color = Colors.darkGreen;
    
    for (final treePos in _treePositions) {
      final x = (treePos.x - _scrollOffset) % (size.x + 100) - 50;
      _drawTree(canvas, Offset(x, treePos.y), treePaint, leafPaint);
    }
    
    // Draw ground line
    final groundLinePaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..strokeWidth = 2;
    canvas.drawLine(
      Offset(0, size.y - 150),
      Offset(size.x, size.y - 150),
      groundLinePaint,
    );
  }
  
  void _drawCloud(Canvas canvas, Offset position, Paint paint) {
    // Draw a simple cloud shape
    canvas.drawCircle(position, 20, paint);
    canvas.drawCircle(position + const Offset(15, 0), 25, paint);
    canvas.drawCircle(position + const Offset(30, 0), 20, paint);
    canvas.drawCircle(position + const Offset(15, -10), 15, paint);
  }
  
  void _drawGrass(Canvas canvas, Offset position, Paint paint) {
    // Draw simple grass blades
    for (int i = 0; i < 3; i++) {
      canvas.drawLine(
        position + Offset(i * 3.0, 0),
        position + Offset(i * 3.0, -10 - _random.nextDouble() * 5),
        paint,
      );
    }
  }
  
  void _drawTree(Canvas canvas, Offset position, Paint trunkPaint, Paint leafPaint) {
    // Draw tree trunk
    canvas.drawRect(
      Rect.fromLTWH(position.dx - 5, position.dy, 10, 40),
      trunkPaint,
    );
    
    // Draw tree leaves (simple circle)
    canvas.drawCircle(
      position + const Offset(0, -20),
      25,
      leafPaint,
    );
  }
}
