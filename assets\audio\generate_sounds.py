#!/usr/bin/env python3
"""
مولد الأصوات للعبة ArzaRush
يقوم بإنشاء ملفات صوتية بسيطة للعبة
"""

import numpy as np
import wave
import os

def create_sound_file(filename, frequency, duration, sample_rate=44100):
    """إنشاء ملف صوتي بسيط"""
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # إنشاء موجة صوتية
    if 'jump' in filename:
        # صوت قفز - تردد متصاعد
        wave_data = np.sin(frequency * 2 * np.pi * t) * np.exp(-t * 3)
    elif 'fly' in filename:
        # صوت طيران - تردد ثابت مع تموج
        wave_data = np.sin(frequency * 2 * np.pi * t) * 0.5 + np.sin(frequency * 1.5 * 2 * np.pi * t) * 0.3
    elif 'coin' in filename:
        # صوت عملة - نغمة عالية قصيرة
        wave_data = np.sin(frequency * 2 * np.pi * t) * np.exp(-t * 5)
    elif 'crash' in filename:
        # صوت اصطدام - ضوضاء
        wave_data = np.random.normal(0, 0.3, len(t)) * np.exp(-t * 2)
    elif 'thunder' in filename:
        # صوت رعد - تردد منخفض مع ضوضاء
        wave_data = np.sin(frequency * 2 * np.pi * t) * 0.7 + np.random.normal(0, 0.2, len(t))
    elif 'fire' in filename:
        # صوت نار - ضوضاء مع تردد متوسط
        wave_data = np.random.normal(0, 0.4, len(t)) * np.sin(frequency * 2 * np.pi * t)
    elif 'explosion' in filename:
        # صوت انفجار - ضوضاء قوية
        wave_data = np.random.normal(0, 0.8, len(t)) * np.exp(-t * 1.5)
    else:
        # صوت افتراضي
        wave_data = np.sin(frequency * 2 * np.pi * t)
    
    # تطبيع الصوت
    wave_data = np.clip(wave_data, -1, 1)
    wave_data = (wave_data * 32767).astype(np.int16)
    
    # حفظ الملف
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # أحادي
        wav_file.setsampwidth(2)  # 16 بت
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(wave_data.tobytes())

def main():
    """إنشاء جميع الأصوات المطلوبة"""
    sounds = [
        ('jump.wav', 440, 0.3),      # صوت قفز
        ('fly.wav', 220, 0.5),       # صوت طيران
        ('land.wav', 330, 0.2),      # صوت هبوط
        ('coin.wav', 880, 0.2),      # صوت عملة
        ('powerup.wav', 660, 0.4),   # صوت قوة خاصة
        ('crash.wav', 150, 0.5),     # صوت اصطدام
        ('explosion.wav', 100, 0.8), # صوت انفجار
        ('thunder.wav', 80, 0.6),    # صوت رعد
        ('fire.wav', 200, 0.4),      # صوت نار
        ('missile.wav', 300, 0.3),   # صوت صاروخ
        ('gameover.wav', 110, 1.0),  # صوت انتهاء اللعبة
    ]
    
    print("إنشاء ملفات الأصوات...")
    for filename, freq, duration in sounds:
        create_sound_file(filename, freq, duration)
        print(f"تم إنشاء: {filename}")
    
    print("تم إنشاء جميع الأصوات بنجاح!")

if __name__ == "__main__":
    main()
