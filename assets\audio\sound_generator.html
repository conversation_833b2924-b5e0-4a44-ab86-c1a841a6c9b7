<!DOCTYPE html>
<html>
<head>
    <title>ArzaRush Sound Generator</title>
</head>
<body>
    <h1>مولد أصوات لعبة ArzaRush</h1>
    <button onclick="generateAllSounds()">إنشاء جميع الأصوات</button>
    <div id="status"></div>

    <script>
        // إنشاء AudioContext
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        function createTone(frequency, duration, type = 'sine') {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = type;
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
            
            return { oscillator, gainNode };
        }

        function playJumpSound() {
            createTone(440, 0.3);
        }

        function playFlySound() {
            createTone(220, 0.5);
        }

        function playCoinSound() {
            createTone(880, 0.2);
        }

        function playCrashSound() {
            createTone(150, 0.5, 'sawtooth');
        }

        function playThunderSound() {
            createTone(80, 0.6, 'square');
        }

        function playFireSound() {
            createTone(200, 0.4, 'sawtooth');
        }

        function playExplosionSound() {
            createTone(100, 0.8, 'square');
        }

        function playMissileSound() {
            createTone(300, 0.3);
        }

        function playGameOverSound() {
            createTone(110, 1.0, 'triangle');
        }

        function generateAllSounds() {
            document.getElementById('status').innerHTML = 'تم إنشاء الأصوات! استخدم الأزرار أدناه لتجربتها:';
            
            const buttons = `
                <br><br>
                <button onclick="playJumpSound()">صوت القفز</button>
                <button onclick="playFlySound()">صوت الطيران</button>
                <button onclick="playCoinSound()">صوت العملة</button>
                <button onclick="playCrashSound()">صوت الاصطدام</button>
                <button onclick="playThunderSound()">صوت الرعد</button>
                <button onclick="playFireSound()">صوت النار</button>
                <button onclick="playExplosionSound()">صوت الانفجار</button>
                <button onclick="playMissileSound()">صوت الصاروخ</button>
                <button onclick="playGameOverSound()">صوت انتهاء اللعبة</button>
            `;
            
            document.getElementById('status').innerHTML += buttons;
        }
    </script>
</body>
</html>
