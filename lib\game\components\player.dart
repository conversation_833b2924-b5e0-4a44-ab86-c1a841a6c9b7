import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../arza_rush_game.dart';
import 'obstacle.dart';
import 'coin.dart';

class Player extends RectangleComponent
    with HasGameRef<ArzaRushGame>, CollisionCallbacks {
  
  // Player properties
  static const double playerWidth = 50.0;
  static const double playerHeight = 60.0;
  static const double jumpHeight = 150.0;
  static const double gravity = 800.0;
  static const double jumpSpeed = -400.0;
  
  // Player state
  double _velocityY = 0.0;
  double _groundY = 0.0;
  bool _isOnGround = true;
  bool _isJumping = false;
  
  // Animation
  late Timer _animationTimer;
  int _currentFrame = 0;
  final List<Color> _animationColors = [
    Colors.blue,
    Colors.lightBlue,
    Colors.cyan,
  ];
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Set player size and position
    size = Vector2(playerWidth, playerHeight);
    _groundY = gameRef.size.y - 150;
    position = Vector2(100, _groundY);
    
    // Set initial color
    paint.color = _animationColors[0];
    
    // Add collision detection
    add(RectangleHitbox());
    
    // Setup animation timer
    _animationTimer = Timer(
      0.2, // Change color every 0.2 seconds
      repeat: true,
      onTick: _updateAnimation,
    );
    _animationTimer.start();
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Update animation timer
    _animationTimer.update(dt);
    
    // Apply gravity
    if (!_isOnGround) {
      _velocityY += gravity * dt;
      position.y += _velocityY * dt;
      
      // Check if player landed
      if (position.y >= _groundY) {
        position.y = _groundY;
        _velocityY = 0.0;
        _isOnGround = true;
        _isJumping = false;
      }
    }
    
    // Add slight bobbing animation when on ground
    if (_isOnGround && !_isJumping) {
      position.y = _groundY + (sin(DateTime.now().millisecondsSinceEpoch / 200) * 2);
    }
  }
  
  void jump() {
    if (_isOnGround) {
      _velocityY = jumpSpeed;
      _isOnGround = false;
      _isJumping = true;
    }
  }
  
  void _updateAnimation() {
    _currentFrame = (_currentFrame + 1) % _animationColors.length;
    paint.color = _animationColors[_currentFrame];
  }
  
  @override
  bool onCollisionStart(
    Set<Vector2> intersectionPoints,
    PositionComponent other,
  ) {
    if (other is Obstacle) {
      // Game over on obstacle collision
      gameRef.gameOver();
      return true;
    } else if (other is Coin) {
      // Collect coin
      other.removeFromParent();
      gameRef.collectCoin();
      return false;
    }
    return true;
  }
  
  void reset() {
    position = Vector2(100, _groundY);
    _velocityY = 0.0;
    _isOnGround = true;
    _isJumping = false;
    _currentFrame = 0;
    paint.color = _animationColors[0];
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Add a simple face to the player
    final paint = Paint()..color = Colors.white;
    
    // Eyes
    canvas.drawCircle(
      Offset(size.x * 0.3, size.y * 0.3),
      3,
      paint,
    );
    canvas.drawCircle(
      Offset(size.x * 0.7, size.y * 0.3),
      3,
      paint,
    );
    
    // Mouth (smile when jumping, neutral when on ground)
    final mouthPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    if (_isJumping) {
      // Happy face when jumping
      canvas.drawArc(
        Rect.fromCenter(
          center: Offset(size.x * 0.5, size.y * 0.6),
          width: 20,
          height: 10,
        ),
        0,
        3.14159, // π radians (180 degrees)
        false,
        mouthPaint,
      );
    } else {
      // Neutral face when on ground
      canvas.drawLine(
        Offset(size.x * 0.4, size.y * 0.65),
        Offset(size.x * 0.6, size.y * 0.65),
        mouthPaint,
      );
    }
  }
}
