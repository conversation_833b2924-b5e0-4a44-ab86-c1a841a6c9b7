import 'dart:async';
import 'dart:math';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'components/player.dart';
import 'components/obstacle.dart';
import 'components/coin.dart';
import 'components/background.dart';

class ArzaRushGame extends FlameGame
    with HasKeyboardHandlerComponents, HasCollisionDetection, HasTappables {
  
  // Game state
  late Player player;
  late Background background;
  late Timer obstacleTimer;
  late Timer coinTimer;
  
  // Game properties
  double gameSpeed = 200.0;
  final double maxGameSpeed = 500.0;
  final double speedIncrement = 2.0;
  
  // Score and game state
  final ValueNotifier<int> scoreNotifier = ValueNotifier<int>(0);
  final ValueNotifier<bool> gameOverNotifier = ValueNotifier<bool>(false);
  int _score = 0;
  bool _isGameOver = false;
  
  // Random generator
  final Random _random = Random();
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Initialize background
    background = Background();
    add(background);
    
    // Initialize player
    player = Player();
    add(player);
    
    // Setup obstacle spawning
    obstacleTimer = Timer(
      2.0, // Spawn obstacle every 2 seconds
      repeat: true,
      onTick: _spawnObstacle,
    );
    
    // Setup coin spawning
    coinTimer = Timer(
      1.5, // Spawn coin every 1.5 seconds
      repeat: true,
      onTick: _spawnCoin,
    );
    
    // Start timers
    obstacleTimer.start();
    coinTimer.start();
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    if (_isGameOver) return;
    
    // Update timers
    obstacleTimer.update(dt);
    coinTimer.update(dt);
    
    // Increase game speed gradually
    if (gameSpeed < maxGameSpeed) {
      gameSpeed += speedIncrement * dt;
    }
    
    // Update score based on distance traveled
    _score += (gameSpeed * dt / 10).round();
    scoreNotifier.value = _score;
  }
  
  @override
  bool onTapDown(TapDownInfo info) {
    if (!_isGameOver) {
      player.jump();
      HapticFeedback.lightImpact();
    }
    return true;
  }
  
  void _spawnObstacle() {
    if (_isGameOver) return;
    
    final obstacle = Obstacle();
    obstacle.position = Vector2(size.x + 50, size.y - 150);
    add(obstacle);
  }
  
  void _spawnCoin() {
    if (_isGameOver) return;
    
    final coin = Coin();
    coin.position = Vector2(
      size.x + 50,
      size.y - 200 - _random.nextDouble() * 100,
    );
    add(coin);
  }
  
  void collectCoin() {
    _score += 10;
    scoreNotifier.value = _score;
    HapticFeedback.lightImpact();
  }
  
  void gameOver() {
    if (_isGameOver) return;
    
    _isGameOver = true;
    gameOverNotifier.value = true;
    
    // Stop timers
    obstacleTimer.stop();
    coinTimer.stop();
    
    // Stop all components
    pauseEngine();
    
    HapticFeedback.heavyImpact();
  }
  
  void resetGame() {
    _isGameOver = false;
    gameOverNotifier.value = false;
    _score = 0;
    scoreNotifier.value = 0;
    gameSpeed = 200.0;
    
    // Remove all obstacles and coins
    children.whereType<Obstacle>().forEach((obstacle) => obstacle.removeFromParent());
    children.whereType<Coin>().forEach((coin) => coin.removeFromParent());
    
    // Reset player
    player.reset();
    
    // Restart timers
    obstacleTimer.start();
    coinTimer.start();
    
    // Resume game
    resumeEngine();
  }
  
  @override
  void onRemove() {
    obstacleTimer.stop();
    coinTimer.stop();
    super.onRemove();
  }
}
