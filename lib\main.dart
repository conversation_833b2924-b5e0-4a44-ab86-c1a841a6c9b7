import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flame_audio/flame_audio.dart';
import 'package:audioplayers/audioplayers.dart';
import 'dart:async';
import 'dart:math';

// مدير الأصوات الحقيقي - يستخدم AudioPlayer مباشرة
class SoundManager {
  static bool _soundEnabled = true;
  static double _volume = 0.7;
  static final AudioPlayer _audioPlayer = AudioPlayer();

  // تشغيل الأصوات الحقيقية باستخدام تردد الصوت
  static Future<void> playSound(String soundType) async {
    if (!_soundEnabled) return;

    try {
      // إنشاء أصوات بسيطة باستخدام SystemSound
      switch (soundType) {
        case 'jump':
        case 'fly':
        case 'land':
          await SystemSound.play(SystemSoundType.click);
          break;
        case 'coin':
        case 'powerup':
          // صوت عملة - نغمة عالية
          await SystemSound.play(SystemSoundType.click);
          await Future.delayed(const Duration(milliseconds: 50));
          await SystemSound.play(SystemSoundType.click);
          break;
        case 'crash':
        case 'explosion':
        case 'thunder':
        case 'fire':
        case 'missile':
        case 'gameover':
          // صوت اصطدام - اهتزاز قوي
          await SystemSound.play(SystemSoundType.alert);
          break;
        default:
          await SystemSound.play(SystemSoundType.click);
      }

      // إضافة اهتزاز مع الصوت
      _playHapticFeedback(soundType);

    } catch (e) {
      // في حالة فشل تشغيل الصوت، استخدم الاهتزاز فقط
      print('خطأ في تشغيل الصوت: $e');
      _playHapticFeedback(soundType);
    }
  }

  // الاهتزاز المصاحب للصوت
  static void _playHapticFeedback(String soundType) {
    switch (soundType) {
      case 'jump':
      case 'fly':
      case 'land':
        HapticFeedback.lightImpact();
        break;
      case 'coin':
      case 'powerup':
        HapticFeedback.selectionClick();
        break;
      case 'crash':
      case 'explosion':
      case 'thunder':
      case 'fire':
      case 'missile':
      case 'gameover':
        HapticFeedback.heavyImpact();
        break;
      default:
        HapticFeedback.lightImpact();
    }
  }



  // تشغيل الموسيقى الخلفية (محاكاة)
  static Future<void> playBackgroundMusic(String musicType) async {
    if (!_soundEnabled) return;
    // محاكاة تشغيل الموسيقى بدون ملفات
  }

  // إيقاف الموسيقى
  static Future<void> stopBackgroundMusic() async {
    // محاكاة إيقاف الموسيقى
  }

  // تبديل الصوت
  static void toggleSound() {
    _soundEnabled = !_soundEnabled;
    if (_soundEnabled) {
      HapticFeedback.lightImpact();
    }
  }

  // تعديل مستوى الصوت
  static void setVolume(double volume) {
    _volume = volume.clamp(0.0, 1.0);
  }

  // الحصول على حالة الصوت
  static bool get isSoundEnabled => _soundEnabled;
  static bool get isMusicEnabled => _soundEnabled; // نفس إعداد الصوت
  static double get volume => _volume;

  // تبديل الموسيقى
  static void toggleMusic() {
    _soundEnabled = !_soundEnabled; // نفس إعداد الصوت
    if (_soundEnabled) {
      HapticFeedback.lightImpact();
    }
  }
}

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations to portrait only
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const ArzaRushApp());
}

class ArzaRushApp extends StatelessWidget {
  const ArzaRushApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ArzaRush - Endless Runner',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.orange,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        fontFamily: 'Arial',
      ),
      home: const MainMenuScreen(),
    );
  }
}

// شاشة القائمة الرئيسية
class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen>
    with TickerProviderStateMixin {
  late AnimationController _titleController;
  late AnimationController _buttonController;
  late Animation<double> _titleAnimation;
  late Animation<Offset> _buttonSlideAnimation;

  @override
  void initState() {
    super.initState();

    // تشغيل موسيقى القائمة الرئيسية
    SoundManager.playBackgroundMusic('menu');

    _titleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _titleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _titleController,
      curve: Curves.elasticOut,
    ));

    _buttonSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.bounceOut,
    ));

    _titleController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _buttonController.forward();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1E3C72),
              Color(0xFF2A5298),
              Color(0xFF3B82F6),
              Color(0xFF8B5CF6),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // عنوان اللعبة مع تأثيرات
                ScaleTransition(
                  scale: _titleAnimation,
                  child: Column(
                    children: [
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [Colors.orange, Colors.red, Colors.purple],
                        ).createShader(bounds),
                        child: const Text(
                          'ARZA',
                          style: TextStyle(
                            fontSize: 80,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [Colors.yellow, Colors.orange, Colors.red],
                        ).createShader(bounds),
                        child: const Text(
                          'RUSH',
                          style: TextStyle(
                            fontSize: 80,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.orange.withOpacity(0.8),
                              Colors.red.withOpacity(0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          'Endless Runner Adventure',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 80),

                // أزرار القائمة
                SlideTransition(
                  position: _buttonSlideAnimation,
                  child: Column(
                    children: [
                      _buildMenuButton(
                        'START GAME',
                        Icons.play_arrow_rounded,
                        [Colors.green.shade400, Colors.green.shade600],
                        () => _startGame(),
                      ),
                      const SizedBox(height: 20),
                      _buildMenuButton(
                        'HIGH SCORES',
                        Icons.emoji_events_rounded,
                        [Colors.yellow.shade400, Colors.orange.shade600],
                        () => _showHighScores(),
                      ),
                      const SizedBox(height: 20),
                      _buildSoundToggleButton(),
                      const SizedBox(height: 20),
                      _buildMenuButton(
                        'SETTINGS',
                        Icons.settings_rounded,
                        [Colors.blue.shade400, Colors.blue.shade600],
                        () => _showSettings(),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 60),

                // معلومات الإصدار
                FadeTransition(
                  opacity: _titleAnimation,
                  child: const Text(
                    'Version 1.0.0 - Made with ❤️',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(
    String text,
    IconData icon,
    List<Color> gradientColors,
    VoidCallback onPressed,
  ) {
    return Container(
      width: 300,
      height: 65,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(35),
        gradient: LinearGradient(colors: gradientColors),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.4),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(35),
          onTap: () {
            HapticFeedback.lightImpact();
            onPressed();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 30,
              ),
              const SizedBox(width: 15),
              Text(
                text,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _startGame() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const ProfessionalGameScreen(),
      ),
    );
  }

  void _showHighScores() {
    // TODO: Implement high scores screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('High Scores coming soon!'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _buildSoundToggleButton() {
    return StatefulBuilder(
      builder: (context, setState) {
        return GestureDetector(
          onTap: () {
            setState(() {
              SoundManager.toggleSound();
            });
            HapticFeedback.lightImpact();
          },
          child: Container(
            width: 280,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: SoundManager.isSoundEnabled
                  ? [Colors.purple.shade400, Colors.purple.shade600]
                  : [Colors.grey.shade400, Colors.grey.shade600],
              ),
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: (SoundManager.isSoundEnabled ? Colors.purple : Colors.grey).withOpacity(0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  SoundManager.isSoundEnabled ? Icons.volume_up_rounded : Icons.volume_off_rounded,
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  SoundManager.isSoundEnabled ? 'SOUND ON' : 'SOUND OFF',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSettings() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'إعدادات اللعبة',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // إعداد الصوت
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'الصوت',
                        style: TextStyle(fontSize: 18),
                      ),
                      Switch(
                        value: SoundManager.isSoundEnabled,
                        onChanged: (value) {
                          setState(() {
                            SoundManager.toggleSound();
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // إعداد الموسيقى
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'الموسيقى',
                        style: TextStyle(fontSize: 18),
                      ),
                      Switch(
                        value: SoundManager.isMusicEnabled,
                        onChanged: (value) {
                          setState(() {
                            SoundManager.toggleMusic();
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // زر إعادة تعيين النقاط
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم إعادة تعيين النقاط'),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('إعادة تعيين النقاط'),
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'إغلاق',
                style: TextStyle(fontSize: 18),
              ),
            ),
          ],
        );
      },
    );
  }
}

// شاشة اللعبة الاحترافية
class ProfessionalGameScreen extends StatefulWidget {
  const ProfessionalGameScreen({super.key});

  @override
  State<ProfessionalGameScreen> createState() => _ProfessionalGameScreenState();
}

class _ProfessionalGameScreenState extends State<ProfessionalGameScreen>
    with TickerProviderStateMixin {

  late ProfessionalArzaRushGame game;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    game = ProfessionalArzaRushGame();
  }

  void _pauseGame() {
    setState(() {
      _isPaused = !_isPaused;
      if (_isPaused) {
        game.pauseEngine();
      } else {
        game.resumeEngine();
      }
    });
  }

  void _goToMainMenu() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const MainMenuScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        // آلية التحكم الجديدة - الضغط المستمر للطيران
        onTapDown: (details) {
          if (!_isPaused) {
            game.startFlying(); // بدء الطيران
          }
        },
        onTapUp: (details) {
          if (!_isPaused) {
            game.stopFlying(); // إيقاف الطيران والسقوط
          }
        },
        onTapCancel: () {
          if (!_isPaused) {
            game.stopFlying(); // إيقاف الطيران عند إلغاء اللمس
          }
        },
        child: Stack(
          children: [
            // Game Widget
            GameWidget<ProfessionalArzaRushGame>.controlled(
              gameFactory: () => game,
            ),

            // Professional UI Overlay
            _buildGameUI(),

            // تعليمات التحكم
            _buildControlInstructions(),

            // Pause Overlay
            if (_isPaused) _buildPauseOverlay(),

            // Level Complete Overlay
            if (game._isLevelComplete && !game.gameOverNotifier.value)
              _buildLevelCompleteOverlay(),

            // Game Over Overlay
            ValueListenableBuilder<bool>(
              valueListenable: game.gameOverNotifier,
              builder: (context, isGameOver, child) {
                if (!isGameOver) return const SizedBox.shrink();
                return _buildGameOverOverlay();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameUI() {
    return Stack(
      children: [
        // Score Display
        Positioned(
          top: 50,
          left: 20,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.orange.shade400,
                  Colors.orange.shade600,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.star_rounded,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 8),
                ValueListenableBuilder<int>(
                  valueListenable: game.scoreNotifier,
                  builder: (context, score, child) {
                    return Text(
                      score.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),

        // Level and Lives Display
        Positioned(
          top: 50,
          right: 20,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Level Display
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.purple.shade400,
                      Colors.purple.shade600,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.purple.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.military_tech, color: Colors.white, size: 20),
                    const SizedBox(width: 5),
                    Text(
                      'المستوى ${game._level}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              // Lives Display
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.red.shade400,
                      Colors.red.shade600,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.red.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ...List.generate(game._lives, (index) =>
                      const Padding(
                        padding: EdgeInsets.only(right: 2),
                        child: Icon(Icons.favorite, color: Colors.white, size: 18),
                      )
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Pause Button
        Positioned(
          top: 120,
          right: 20,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.shade400,
                  Colors.blue.shade600,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: IconButton(
              onPressed: _pauseGame,
              icon: Icon(
                _isPaused ? Icons.play_arrow_rounded : Icons.pause_rounded,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        ),

        // Power-ups Status
        Positioned(
          top: 180,
          right: 20,
          child: Column(
            children: [
              if (game._hasShield)
                Container(
                  margin: const EdgeInsets.only(bottom: 5),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.shield, color: Colors.white, size: 16),
                      const SizedBox(width: 5),
                      Text(
                        '${game._shieldTimer.toStringAsFixed(1)}s',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              if (game._hasMagnet)
                Container(
                  margin: const EdgeInsets.only(bottom: 5),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.attractions, color: Colors.white, size: 16),
                      const SizedBox(width: 5),
                      Text(
                        '${game._magnetTimer.toStringAsFixed(1)}s',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              if (game._hasSpeedBoost)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.speed, color: Colors.white, size: 16),
                      const SizedBox(width: 5),
                      Text(
                        '${game._speedBoostTimer.toStringAsFixed(1)}s',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildControlInstructions() {
    return Positioned(
      bottom: 30,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(150),
          borderRadius: BorderRadius.circular(15),
        ),
        child: const Text(
          '🎮 اضغط باستمرار للطيران • اترك الشاشة للسقوط',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildPauseOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.blue.shade400,
                    Colors.purple.shade600,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.pause_circle_filled_rounded,
                    color: Colors.white,
                    size: 80,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'PAUSED',
                    style: TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 30),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildPauseButton(
                        'RESUME',
                        Icons.play_arrow_rounded,
                        Colors.green,
                        _pauseGame,
                      ),
                      const SizedBox(width: 20),
                      _buildPauseButton(
                        'MENU',
                        Icons.home_rounded,
                        Colors.red,
                        _goToMainMenu,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLevelCompleteOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.9),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.green.shade400,
                    Colors.blue.shade600,
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withOpacity(0.4),
                    blurRadius: 25,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.emoji_events_rounded,
                    color: Colors.white,
                    size: 80,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'مستوى ${game._level} مكتمل!',
                    style: const TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 15),
                  ValueListenableBuilder<int>(
                    valueListenable: game.scoreNotifier,
                    builder: (context, score, child) {
                      return Column(
                        children: [
                          Text(
                            'النقاط: $score',
                            style: const TextStyle(
                              fontSize: 24,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            'مكافأة: +1000 نقطة',
                            style: const TextStyle(
                              fontSize: 20,
                              color: Colors.yellow,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            'روح إضافية: ❤️',
                            style: const TextStyle(
                              fontSize: 18,
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 35),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildGameOverButton(
                        'المستوى التالي',
                        Icons.arrow_forward_rounded,
                        Colors.green,
                        () {
                          try {
                            game._nextLevel();
                            setState(() {
                              // تحديث الواجهة
                            });
                          } catch (e) {
                            print('خطأ في الانتقال للمستوى التالي: $e');
                            // في حالة الخطأ، إعادة تشغيل اللعبة
                            game.resetGame();
                            setState(() {});
                          }
                        },
                      ),
                      const SizedBox(width: 20),
                      _buildGameOverButton(
                        'القائمة الرئيسية',
                        Icons.home_rounded,
                        Colors.blue,
                        _goToMainMenu,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameOverOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.9),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.red.shade400,
                    Colors.orange.shade600,
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withOpacity(0.4),
                    blurRadius: 25,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.sentiment_dissatisfied_rounded,
                    color: Colors.white,
                    size: 80,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'GAME OVER',
                    style: TextStyle(
                      fontSize: 42,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 15),
                  ValueListenableBuilder<int>(
                    valueListenable: game.scoreNotifier,
                    builder: (context, score, child) {
                      return Column(
                        children: [
                          Text(
                            'النقاط النهائية: $score',
                            style: const TextStyle(
                              fontSize: 24,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            'وصلت للمستوى: ${game._level}',
                            style: const TextStyle(
                              fontSize: 20,
                              color: Colors.yellow,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 35),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildGameOverButton(
                        'العب مرة أخرى',
                        Icons.refresh_rounded,
                        Colors.green,
                        () => game.resetGame(),
                      ),
                      const SizedBox(width: 20),
                      _buildGameOverButton(
                        'القائمة الرئيسية',
                        Icons.home_rounded,
                        Colors.blue,
                        _goToMainMenu,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPauseButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
      ),
    );
  }

  Widget _buildGameOverButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }
}

// لعبة ArzaRush الاحترافية
class ProfessionalArzaRushGame extends FlameGame with HasCollisionDetection {
  // Game state
  final ValueNotifier<int> scoreNotifier = ValueNotifier<int>(0);
  final ValueNotifier<bool> gameOverNotifier = ValueNotifier<bool>(false);
  int _score = 0;
  bool _isGameOver = false;

  // نظام المستويات والميزات الجديدة
  int _level = 1;
  int _lives = 3;
  bool _hasShield = false;
  bool _hasMagnet = false;
  bool _hasSpeedBoost = false;
  double _shieldTimer = 0.0;
  double _magnetTimer = 0.0;
  double _speedBoostTimer = 0.0;

  // نظام المستويات المنفصلة
  bool _isLevelComplete = false;
  int _targetScore = 1000; // النقاط المطلوبة لإنهاء المستوى
  LevelType _currentLevelType = LevelType.city;

  // Game components
  late ProfessionalPlayer player;
  late ProfessionalGround ground;
  late ProfessionalBackground background;

  // Game speed
  double gameSpeed = 250.0;
  final double maxGameSpeed = 500.0;

  // Timers for spawning
  late Timer obstacleTimer;
  late Timer coinTimer;
  late Timer powerUpTimer;
  late Timer airObstacleTimer; // للعوائق الجوية

  // عدادات العوائق المتقدمة
  late Timer _topObstacleTimer;
  late Timer _bottomObstacleTimer;
  late Timer _leftObstacleTimer;

  final Random _random = Random();

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // تشغيل الموسيقى الخلفية
    SoundManager.playBackgroundMusic('background');

    // Create background with parallax effect
    background = ProfessionalBackground();
    add(background);

    // Create ground with moving pattern
    ground = ProfessionalGround();
    add(ground);

    // Create player with animations
    player = ProfessionalPlayer();
    add(player);

    // Setup obstacle spawning with difficulty scaling
    obstacleTimer = Timer(
      2.5,
      repeat: true,
      onTick: _spawnObstacle,
    );

    // إضافة مؤقت للعوائق الأرضية
    Timer(
      1.8,
      repeat: true,
      onTick: _spawnGroundObstacle,
    ).start();

    // عوائق في مستويات مختلفة (أعلى، وسط، أسفل)
    _topObstacleTimer = Timer(
      3.0,
      repeat: true,
      onTick: _spawnHighAltitudeObstacle,
    );
    _topObstacleTimer.start();

    _bottomObstacleTimer = Timer(
      2.8,
      repeat: true,
      onTick: _spawnMidAltitudeObstacle,
    );
    _bottomObstacleTimer.start();

    _leftObstacleTimer = Timer(
      3.5,
      repeat: true,
      onTick: _spawnLowAltitudeObstacle,
    );
    _leftObstacleTimer.start();

    // Setup coin spawning - أكثر تكراراً
    coinTimer = Timer(
      1.2,
      repeat: true,
      onTick: _spawnCoin,
    );

    // Setup power-up spawning - أكثر تكراراً
    powerUpTimer = Timer(
      5.0,
      repeat: true,
      onTick: _spawnPowerUp,
    );

    // إضافة مؤقت للعملات الخاصة
    Timer(
      3.0,
      repeat: true,
      onTick: _spawnSpecialCoin,
    ).start();

    // إضافة مؤقت للمكافآت
    Timer(
      10.0,
      repeat: true,
      onTick: _spawnBonus,
    ).start();

    // Setup air obstacles spawning (طيور صاعقة وصواريخ طائرة)
    airObstacleTimer = Timer(
      3.0,
      repeat: true,
      onTick: _spawnAirObstacle,
    );

    // Start all timers
    obstacleTimer.start();
    coinTimer.start();
    powerUpTimer.start();
    airObstacleTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (_isGameOver) return;

    // Update timers
    obstacleTimer.update(dt);
    coinTimer.update(dt);
    powerUpTimer.update(dt);
    _topObstacleTimer.update(dt);
    _bottomObstacleTimer.update(dt);
    _leftObstacleTimer.update(dt);

    // تحديث مؤقتات القوى الخاصة
    if (_hasShield) {
      _shieldTimer -= dt;
      if (_shieldTimer <= 0) {
        _hasShield = false;
      }
    }

    if (_hasMagnet) {
      _magnetTimer -= dt;
      if (_magnetTimer <= 0) {
        _hasMagnet = false;
      }
    }

    if (_hasSpeedBoost) {
      _speedBoostTimer -= dt;
      if (_speedBoostTimer <= 0) {
        _hasSpeedBoost = false;
      }
    }

    // Increase game speed gradually
    if (gameSpeed < maxGameSpeed) {
      gameSpeed += 15 * dt;
    }

    // Update score based on distance
    _score += (gameSpeed * dt / 15).round();
    scoreNotifier.value = _score;

    // نظام المستويات الجديد
    if (_score >= _targetScore && !_isLevelComplete && !_isGameOver) {
      _isLevelComplete = true;
      _completeLevel();
    }

    // Update difficulty - spawn obstacles faster as score increases
    if (_score > 500 && obstacleTimer.limit > 1.5) {
      obstacleTimer.limit = 1.5;
    }
    if (_score > 1000 && obstacleTimer.limit > 1.2) {
      obstacleTimer.limit = 1.2;
    }

    // زيادة صعوبة العوائق المتحركة
    if (_level > 2) {
      _topObstacleTimer.limit = max(1.5, 2.5 - (_level * 0.2));
      _bottomObstacleTimer.limit = max(2.0, 3.0 - (_level * 0.2));
      _leftObstacleTimer.limit = max(1.8, 2.8 - (_level * 0.2));
    }
  }

  // دالة إكمال المستوى
  void _completeLevel() {
    // إيقاف اللعبة مؤقتاً
    obstacleTimer.stop();
    coinTimer.stop();
    powerUpTimer.stop();
    airObstacleTimer.stop();
    _topObstacleTimer.stop();
    _bottomObstacleTimer.stop();
    _leftObstacleTimer.stop();

    // تشغيل صوت إكمال المستوى
    SoundManager.playSound('levelup');

    // إضافة نقاط مكافأة
    _score += 1000;
    scoreNotifier.value = _score;

    // زيادة الأرواح
    if (_lives < 5) {
      _lives++;
    }

    HapticFeedback.heavyImpact();

    // إظهار شاشة إكمال المستوى
    _showLevelCompleteScreen();
  }

  void _showLevelCompleteScreen() {
    // إيقاف جميع المؤقتات
    obstacleTimer.stop();
    coinTimer.stop();
    powerUpTimer.stop();
    airObstacleTimer.stop();
    _topObstacleTimer.stop();
    _bottomObstacleTimer.stop();
    _leftObstacleTimer.stop();

    // إظهار شاشة إكمال المستوى
    overlays.add('LevelComplete');
  }

  void _nextLevel() {
    // إعطاء مكافأة المستوى
    _score += 1000;
    _lives = min(_lives + 1, 5); // إضافة روح (حد أقصى 5)

    _level++;
    _isLevelComplete = false;
    _targetScore = _score + (1000 * _level); // زيادة الهدف

    // تغيير نوع المستوى
    final levelTypes = LevelType.values;
    _currentLevelType = levelTypes[(_level - 1) % levelTypes.length];

    // إزالة العوائق الحالية بأمان
    final obstaclesToRemove = <Component>[];
    for (final child in children) {
      if (child is SkyObstacle ||
          child is GroundObstacle ||
          child is AirObstacle ||
          child is ProfessionalCoin ||
          child is ProfessionalPowerUp ||
          child is AirProjectile) {
        obstaclesToRemove.add(child);
      }
    }

    for (final obstacle in obstaclesToRemove) {
      obstacle.removeFromParent();
    }

    // إعادة تعيين حالة اللاعب
    player.reset();

    // إعادة إنشاء الخلفية
    try {
      background.currentLevelType = _currentLevelType;
      background._initializeEnvironment();
    } catch (e) {
      print('خطأ في إعادة إنشاء الخلفية: $e');
    }

    // إعادة تشغيل المؤقتات
    obstacleTimer.start();
    coinTimer.start();
    powerUpTimer.start();
    airObstacleTimer.start();
    _topObstacleTimer.start();
    _bottomObstacleTimer.start();
    _leftObstacleTimer.start();

    // زيادة سرعة اللعبة تدريجياً
    gameSpeed = min(gameSpeed + 30, 500); // حد أقصى للسرعة

    // تقليل فترات ظهور العوائق
    if (_level > 2) {
      obstacleTimer.limit = max(1.0, 2.5 - (_level * 0.05));
      _topObstacleTimer.limit = max(1.5, 3.0 - (_level * 0.05));
      _bottomObstacleTimer.limit = max(2.0, 2.8 - (_level * 0.05));
      _leftObstacleTimer.limit = max(1.8, 3.5 - (_level * 0.05));
    }

    // تحديث النقاط
    scoreNotifier.value = _score;
  }

  void _spawnObstacle() {
    if (_isGameOver) return;

    // إنشاء عوائق جوية فقط - لا عوائق أرضية
    final obstacle = SkyObstacle();

    // العوائق تظهر في مستويات مختلفة من السماء
    final skyLevels = [
      size.y * 0.2,  // مستوى عالي
      size.y * 0.4,  // مستوى متوسط
      size.y * 0.6,  // مستوى منخفض
    ];

    obstacle.position = Vector2(
      size.x + 50,
      skyLevels[Random().nextInt(skyLevels.length)],
    );
    add(obstacle);
  }

  void _spawnCoin() {
    if (_isGameOver) return;

    final coin = ProfessionalCoin();
    coin.position = Vector2(
      size.x + 50,
      size.y - 200 - _random.nextDouble() * 100,
    );
    add(coin);
  }

  void _spawnPowerUp() {
    if (_isGameOver) return;

    final powerUp = ProfessionalPowerUp();
    powerUp.position = Vector2(
      size.x + 50,
      size.y - 180 - _random.nextDouble() * 80,
    );
    add(powerUp);
  }

  void _spawnAirObstacle() {
    if (_isGameOver) return;

    final airObstacle = AirObstacle();
    // العوائق الجوية تظهر في الجزء العلوي من الشاشة
    airObstacle.position = Vector2(
      size.x + 50,
      100 + _random.nextDouble() * 200, // في الجو بين 100-300 بكسل من الأعلى
    );
    add(airObstacle);
  }

  void _spawnGroundObstacle() {
    if (_isGameOver) return;

    final groundObstacle = GroundObstacle();
    // العوائق الأرضية تظهر على الأرض
    groundObstacle.position = Vector2(
      size.x + 50,
      size.y - 150, // على الأرض
    );
    add(groundObstacle);
  }

  void _spawnSpecialCoin() {
    if (_isGameOver) return;

    final specialCoin = SpecialCoin();
    // العملات الخاصة تظهر في مستويات مختلفة
    final levels = [size.y * 0.3, size.y * 0.5, size.y * 0.7];
    specialCoin.position = Vector2(
      size.x + 50,
      levels[Random().nextInt(levels.length)],
    );
    add(specialCoin);
  }

  void _spawnBonus() {
    if (_isGameOver) return;

    final bonus = BonusItem();
    // المكافآت تظهر في مستويات مختلفة
    bonus.position = Vector2(
      size.x + 50,
      size.y * 0.4 + Random().nextDouble() * size.y * 0.3,
    );
    add(bonus);
  }

  // عوائق في الارتفاع العالي (السماء العليا)
  void _spawnHighAltitudeObstacle() {
    if (_isGameOver || _isLevelComplete) return;

    final obstacle = SkyObstacle();
    obstacle.position = Vector2(
      size.x + 50, // من اليمين
      50 + _random.nextDouble() * 150, // في الأعلى
    );
    add(obstacle);
  }

  // عوائق في الارتفاع المتوسط (وسط الشاشة)
  void _spawnMidAltitudeObstacle() {
    if (_isGameOver || _isLevelComplete) return;

    final obstacle = AirObstacle();
    obstacle.position = Vector2(
      size.x + 50, // من اليمين
      200 + _random.nextDouble() * 200, // في الوسط
    );
    add(obstacle);
  }

  // عوائق في الارتفاع المنخفض (قريب من الأرض)
  void _spawnLowAltitudeObstacle() {
    if (_isGameOver || _isLevelComplete) return;

    final obstacle = GroundObstacle();
    obstacle.position = Vector2(
      size.x + 50, // من اليمين
      size.y - 200 + _random.nextDouble() * 100, // قريب من الأرض
    );
    add(obstacle);
  }

  void jump() {
    if (!_isGameOver) {
      player.jump();
      HapticFeedback.lightImpact();
    }
  }

  // آلية الطيران الجديدة
  void startFlying() {
    if (!_isGameOver) {
      player.startFlying();
      HapticFeedback.lightImpact();
    }
  }

  void stopFlying() {
    if (!_isGameOver) {
      player.stopFlying();
    }
  }

  void collectCoin() {
    _score += 50;
    scoreNotifier.value = _score;
    HapticFeedback.lightImpact();
  }

  void collectPowerUp() {
    _score += 100;
    scoreNotifier.value = _score;
    // TODO: Add power-up effects
    HapticFeedback.mediumImpact();
  }

  void gameOver() {
    if (_isGameOver) return;

    _isGameOver = true;
    gameOverNotifier.value = true;

    // تشغيل صوت انتهاء اللعبة
    SoundManager.playSound('gameover');

    // إيقاف الموسيقى الخلفية
    SoundManager.stopBackgroundMusic();

    obstacleTimer.stop();
    coinTimer.stop();
    powerUpTimer.stop();
    airObstacleTimer.stop();

    HapticFeedback.heavyImpact();
  }

  void resetGame() {
    _isGameOver = false;
    gameOverNotifier.value = false;
    _score = 0;
    scoreNotifier.value = 0;
    gameSpeed = 250.0;

    // إعادة تعيين نظام المستويات والميزات
    _level = 1;
    _lives = 3;
    _hasShield = false;
    _hasMagnet = false;
    _hasSpeedBoost = false;
    _shieldTimer = 0.0;
    _magnetTimer = 0.0;
    _speedBoostTimer = 0.0;

    // إعادة تشغيل الموسيقى الخلفية
    SoundManager.playBackgroundMusic('background');

    // Remove all game objects
    children.whereType<SkyObstacle>().forEach((obstacle) => obstacle.removeFromParent());
    children.whereType<GroundObstacle>().forEach((obstacle) => obstacle.removeFromParent());

    children.whereType<ProfessionalCoin>().forEach((coin) => coin.removeFromParent());
    children.whereType<ProfessionalPowerUp>().forEach((powerUp) => powerUp.removeFromParent());
    children.whereType<AirObstacle>().forEach((airObstacle) => airObstacle.removeFromParent());
    children.whereType<AirProjectile>().forEach((projectile) => projectile.removeFromParent());

    // Reset player
    player.reset();

    // Reset timers
    obstacleTimer.limit = 2.5;
    obstacleTimer.start();
    coinTimer.start();
    powerUpTimer.start();
    airObstacleTimer.start();
    _topObstacleTimer.start();
    _bottomObstacleTimer.start();
    _leftObstacleTimer.start();
  }
}

// Professional Player Component
class ProfessionalPlayer extends RectangleComponent
    with HasGameRef<ProfessionalArzaRushGame>, CollisionCallbacks {

  static const double playerWidth = 50.0;
  static const double playerHeight = 70.0;
  static const double jumpSpeed = -650.0; // قفز أقوى وأسرع
  static const double gravity = 1200.0; // جاذبية أقوى للهبوط السريع
  static const double flySpeed = -300.0; // سرعة الطيران المستمر

  double _velocityY = 0.0;
  double _groundY = 0.0;
  bool _isOnGround = true;
  bool _isFlying = false; // حالة الطيران المستمر

  // Animation properties
  late Timer _animationTimer;
  int _currentFrame = 0;
  final List<Color> _runColors = [
    Colors.blue.shade400,
    Colors.blue.shade500,
    Colors.blue.shade600,
  ];

  // متغيرات للعيون والفم التفاعلية
  Vector2 _eyeDirection = Vector2(1, 0); // اتجاه النظر (يمين بشكل افتراضي)
  double _mouthState = 0.8; // 0 = حزين، 0.5 = عادي، 1 = سعيد
  bool _isBlinking = false;
  late Timer _blinkTimer;
  late Timer _eyeTrackingTimer;
  bool _isCrashed = false;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    size = Vector2(playerWidth, playerHeight);
    _groundY = gameRef.size.y - 150;
    position = Vector2(100, _groundY);

    // إزالة لون الخلفية - جعل الشخصية شفافة
    paint.color = Colors.transparent;

    add(RectangleHitbox());

    // Setup running animation
    _animationTimer = Timer(
      0.15,
      repeat: true,
      onTick: _updateAnimation,
    );
    _animationTimer.start();

    // إعداد مؤقت الرمش
    _blinkTimer = Timer(
      2.0 + Random().nextDouble() * 3.0, // رمش عشوائي كل 2-5 ثواني
      repeat: true,
      onTick: _blink,
    );
    _blinkTimer.start();

    // إعداد مؤقت تتبع العيون
    _eyeTrackingTimer = Timer(
      0.1,
      repeat: true,
      onTick: _updateEyeTracking,
    );
    _eyeTrackingTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    _animationTimer.update(dt);
    _blinkTimer.update(dt);
    _eyeTrackingTimer.update(dt);

    // آلية الطيران الجديدة
    if (_isFlying) {
      // الطيران المستمر - البقاء في الأعلى
      _velocityY = flySpeed;
      position.y += _velocityY * dt;

      // منع الطيران أعلى من حد معين
      if (position.y < 100) {
        position.y = 100;
        _velocityY = 0;
      }

      _isOnGround = false;
    } else {
      // الجاذبية العادية عند عدم الطيران
      if (!_isOnGround) {
        _velocityY += gravity * dt;
        position.y += _velocityY * dt;

        if (position.y >= _groundY) {
          position.y = _groundY;
          _velocityY = 0.0;
          _isOnGround = true;
        }
      }

      // Ground bobbing effect when running
      if (_isOnGround) {
        position.y = _groundY + (sin(DateTime.now().millisecondsSinceEpoch / 150) * 2);
      }
    }
  }

  void jump() {
    if (_isOnGround) {
      _velocityY = jumpSpeed;
      _isOnGround = false;

      // تشغيل صوت القفز
      SoundManager.playSound('jump');

      // تأثير بصري للقفز
      Future.delayed(const Duration(milliseconds: 200), () {
        // يمكن إضافة تأثيرات إضافية هنا
      });
    }
  }

  // دوال الطيران الجديدة
  void startFlying() {
    if (!_isFlying) {
      // تشغيل صوت الطيران عند البدء
      SoundManager.playSound('fly');
    }
    _isFlying = true;
    _isOnGround = false;
  }

  void stopFlying() {
    if (_isFlying) {
      // تشغيل صوت الهبوط
      SoundManager.playSound('land');
    }
    _isFlying = false;
    // السماح للجاذبية بالعمل
  }

  void _updateAnimation() {
    _currentFrame = (_currentFrame + 1) % _runColors.length;
    // لا نحتاج لتغيير اللون بعد الآن لأن الشخصية شفافة
  }

  // دالة الرمش
  void _blink() {
    _isBlinking = true;
    Future.delayed(const Duration(milliseconds: 150), () {
      _isBlinking = false;
    });
  }

  // دالة تتبع العيون للعوائق
  void _updateEyeTracking() {
    if (_isCrashed) return;

    // البحث عن أقرب عائق
    PositionComponent? nearestObstacle;
    double nearestDistance = double.infinity;

    // البحث في العوائق الجوية والأرضية
    for (final child in gameRef.children) {
      if (child is SkyObstacle || child is GroundObstacle || child is AirObstacle) {
        final obstacle = child as PositionComponent;
        final distance = (obstacle.position - position).length;
        if (distance < nearestDistance && obstacle.position.x > position.x) {
          nearestDistance = distance;
          nearestObstacle = obstacle;
        }
      }
    }

    if (nearestObstacle != null) {
      // حساب اتجاه النظر نحو العائق
      final direction = (nearestObstacle.position - position).normalized();
      _eyeDirection = direction;

      // تحديد حالة الفم حسب المسافة
      if (nearestDistance < 200) {
        _mouthState = 0.2; // قلق
      } else if (nearestDistance < 400) {
        _mouthState = 0.5; // عادي
      } else {
        _mouthState = 0.8; // سعيد
      }
    } else {
      // لا توجد عوائق - نظرة سعيدة للأمام
      _eyeDirection = Vector2(1, 0);
      _mouthState = 0.9;
    }

    // تحديث اتجاه النظر حسب الحركة
    if (_isFlying) {
      _eyeDirection.y = -0.3; // النظر للأعلى أثناء الطيران
      _mouthState = 0.9; // سعيد أثناء الطيران
    } else if (_velocityY > 0) {
      _eyeDirection.y = 0.3; // النظر للأسفل أثناء الهبوط
    }
  }

  void reset() {
    position = Vector2(100, _groundY);
    _velocityY = 0.0;
    _isOnGround = true;
    _isFlying = false; // إيقاف الطيران
    _currentFrame = 0;
    paint.color = Colors.transparent; // الحفاظ على الشفافية

    // إعادة تعيين حالة الوجه
    _isCrashed = false;
    _mouthState = 0.8; // سعيد
    _eyeDirection = Vector2(1, 0); // النظر للأمام
    _isBlinking = false;
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    // التحقق من وجود الدرع
    if (gameRef._hasShield && (other is SkyObstacle || other is GroundObstacle || other is AirObstacle)) {
      // الدرع يحمي من العوائق
      SoundManager.playSound('shield');
      other.removeFromParent();
      return false;
    }

    if (other is SkyObstacle || other is GroundObstacle) {
      return _handleObstacleCollision(other);
    } else if (other is AirObstacle) {
      return _handleObstacleCollision(other);

    } else if (other is AirProjectile) {
      // الاصطدام مع المقذوفات (البرق، النار، الصواريخ)
      other.removeFromParent();

      // تشغيل صوت حسب نوع المقذوف
      switch (other.type) {
        case ProjectileType.thunder:
          SoundManager.playSound('thunder');
          break;
        case ProjectileType.fire:
          SoundManager.playSound('fire');
          break;
        case ProjectileType.missile:
          SoundManager.playSound('explosion');
          break;
      }

      gameRef.gameOver();
      return true;
    } else if (other is ProfessionalCoin) {
      other.removeFromParent();
      // تشغيل صوت جمع العملة
      SoundManager.playSound('coin');
      gameRef.collectCoin();
      return false;
    } else if (other is SpecialCoin) {
      other.removeFromParent();
      // تشغيل صوت جمع العملة الخاصة
      SoundManager.playSound('coin');
      gameRef.collectCoin(); // استخدام الدالة الموجودة
      return false;
    } else if (other is BonusItem) {
      other.removeFromParent();
      // تشغيل صوت المكافأة
      SoundManager.playSound('powerup');
      gameRef.collectPowerUp(); // استخدام الدالة الموجودة
      return false;
    } else if (other is ProfessionalPowerUp) {
      other.removeFromParent();
      // تشغيل صوت القوة الخاصة
      SoundManager.playSound('powerup');
      gameRef.collectPowerUp();
      return false;
    }
    return true;
  }

  // دالة التعامل مع اصطدام العوائق
  bool _handleObstacleCollision(PositionComponent obstacle) {
    // تشغيل صوت الاصطدام
    SoundManager.playSound('crash');

    // تحديث حالة الوجه للحزن
    _isCrashed = true;
    _mouthState = 0.0; // فم حزين
    _eyeDirection = Vector2(0, 1); // النظر للأسفل

    // إزالة العائق
    obstacle.removeFromParent();

    // تقليل الأرواح
    gameRef._lives--;

    if (gameRef._lives <= 0) {
      // إنهاء اللعبة
      gameRef.gameOver();
    } else {
      // إعطاء حماية مؤقتة
      gameRef._hasShield = true;
      gameRef._shieldTimer = 2.0; // حماية لمدة ثانيتين

      // تشغيل صوت فقدان الروح
      SoundManager.playSound('hurt');
    }

    return true;
  }

  @override
  void render(Canvas canvas) {
    // لا نستدعي super.render(canvas) لتجنب رسم المكعب الأزرق

    // رسم شخصية كرتونية احترافية فقط
    _drawCartoonCharacter(canvas);
  }

  void _drawCartoonCharacter(Canvas canvas) {
    // الرأس (دائرة كبيرة)
    final headPaint = Paint()
      ..color = const Color(0xFFFFDBB5) // لون البشرة
      ..style = PaintingStyle.fill;

    final headCenter = Offset(size.x * 0.5, size.y * 0.3);
    canvas.drawCircle(headCenter, size.x * 0.35, headPaint);

    // الشعر
    final hairPaint = Paint()
      ..color = const Color(0xFF8B4513) // بني
      ..style = PaintingStyle.fill;

    final hairPath = Path();
    hairPath.addArc(
      Rect.fromCenter(
        center: headCenter,
        width: size.x * 0.7,
        height: size.x * 0.5,
      ),
      -3.14159,
      3.14159,
    );
    canvas.drawPath(hairPath, hairPaint);

    // العيون التفاعلية (تنظر نحو العوائق)
    _drawRealisticEyes(canvas);

    // الأنف
    final nosePaint = Paint()
      ..color = const Color(0xFFFFB6C1) // وردي فاتح
      ..style = PaintingStyle.fill;
    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.32), 2, nosePaint);

    // الفم التفاعلي (يتغير حسب الحالة)
    _drawRealisticMouth(canvas);

    // الجسم
    final bodyPaint = Paint()
      ..color = Colors.blue.shade600
      ..style = PaintingStyle.fill;

    final bodyRect = Rect.fromLTWH(
      size.x * 0.2,
      size.y * 0.5,
      size.x * 0.6,
      size.y * 0.35,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(bodyRect, const Radius.circular(8)),
      bodyPaint,
    );

    // الأذرع (في وضعية الجري إلى الأمام)
    final armPaint = Paint()
      ..color = const Color(0xFFFFDBB5) // لون البشرة
      ..style = PaintingStyle.fill;

    // حركة الأذرع أثناء الجري
    final armSwing = sin(DateTime.now().millisecondsSinceEpoch / 200) * 10;

    // الذراع اليسرى (تتحرك للأمام والخلف)
    canvas.drawCircle(Offset(size.x * 0.15, size.y * 0.6), 8, armPaint);
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.15, size.y * 0.6 + armSwing, size.x * 0.2, 6),
      armPaint,
    );

    // الذراع اليمنى (تتحرك عكس اليسرى)
    canvas.drawCircle(Offset(size.x * 0.85, size.y * 0.6), 8, armPaint);
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.65, size.y * 0.6 - armSwing, size.x * 0.2, 6),
      armPaint,
    );

    // الأرجل (في وضعية الجري إلى الأمام)
    final legPaint = Paint()
      ..color = Colors.blue.shade800
      ..style = PaintingStyle.fill;

    // حركة الأرجل أثناء الجري
    final legSwing = sin(DateTime.now().millisecondsSinceEpoch / 150) * 8;

    // الرجل اليسرى (تتحرك للأمام والخلف)
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.35 + legSwing, size.y * 0.85, 8, size.y * 0.15),
      legPaint,
    );

    // الرجل اليمنى (تتحرك عكس اليسرى)
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.57 - legSwing, size.y * 0.85, 8, size.y * 0.15),
      legPaint,
    );

    // الزلاجة الطائرة (Hoverboard)
    if (_isFlying) {
      _drawHoverboard(canvas);
    } else {
      // الأحذية العادية عند المشي (تتحرك مع الأرجل)
      final shoePaint = Paint()
        ..color = Colors.brown.shade800
        ..style = PaintingStyle.fill;

      // حركة الأحذية مع الأرجل
      final legSwing = sin(DateTime.now().millisecondsSinceEpoch / 150) * 8;

      // الحذاء الأيسر (يتحرك مع الرجل اليسرى)
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(size.x * 0.32 + legSwing, size.y * 0.95, 18, 8),
          const Radius.circular(4),
        ),
        shoePaint,
      );

      // الحذاء الأيمن (يتحرك مع الرجل اليمنى)
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(size.x * 0.54 - legSwing, size.y * 0.95, 18, 8),
          const Radius.circular(4),
        ),
        shoePaint,
      );
    }

    // تأثير الحركة (خطوط السرعة)
    if (!_isOnGround) {
      final speedLinePaint = Paint()
        ..color = Colors.white.withAlpha(150)
        ..strokeWidth = 2;

      for (int i = 0; i < 3; i++) {
        canvas.drawLine(
          Offset(size.x * 0.1, size.y * 0.3 + i * 8),
          Offset(size.x * 0.2, size.y * 0.3 + i * 8),
          speedLinePaint,
        );
      }
    }

    // تأثير الهبوط (غبار)
    if (_isOnGround && _velocityY == 0) {
      final dustPaint = Paint()
        ..color = Colors.brown.withAlpha(100)
        ..style = PaintingStyle.fill;

      for (int i = 0; i < 5; i++) {
        canvas.drawCircle(
          Offset(
            size.x * 0.2 + i * 8 + sin(DateTime.now().millisecondsSinceEpoch / 100 + i) * 3,
            size.y + 5,
          ),
          2 + sin(DateTime.now().millisecondsSinceEpoch / 150 + i) * 1,
          dustPaint,
        );
      }
    }
  }

  // رسم الزلاجة الطائرة
  void _drawHoverboard(Canvas canvas) {
    // جسم الزلاجة
    final hoverboardPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          Colors.cyan.shade400,
          Colors.blue.shade600,
          Colors.purple.shade600,
        ],
      ).createShader(Rect.fromLTWH(size.x * 0.1, size.y + 5, size.x * 0.8, 12));

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.1, size.y + 5, size.x * 0.8, 12),
        const Radius.circular(6),
      ),
      hoverboardPaint,
    );

    // تأثير الطاقة تحت الزلاجة
    final energyPaint = Paint()
      ..color = Colors.cyan.withAlpha(150)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.15, size.y + 18, size.x * 0.7, 6),
        const Radius.circular(3),
      ),
      energyPaint,
    );

    // جزيئات الطاقة المتحركة
    final particlePaint = Paint()..color = Colors.cyan.shade200;

    for (int i = 0; i < 5; i++) {
      final x = size.x * 0.2 + i * (size.x * 0.6 / 4);
      final y = size.y + 20 + sin(DateTime.now().millisecondsSinceEpoch / 100 + i) * 3;
      canvas.drawCircle(Offset(x, y), 2, particlePaint);
    }

    // أضواء LED على الزلاجة
    final ledPaint = Paint()..color = Colors.white;
    canvas.drawCircle(Offset(size.x * 0.2, size.y + 11), 1.5, ledPaint);
    canvas.drawCircle(Offset(size.x * 0.8, size.y + 11), 1.5, ledPaint);

    // خطوط الطاقة على الجانبين
    final energyLinePaint = Paint()
      ..color = Colors.cyan.shade300
      ..strokeWidth = 2;

    canvas.drawLine(
      Offset(size.x * 0.15, size.y + 8),
      Offset(size.x * 0.85, size.y + 8),
      energyLinePaint,
    );

    canvas.drawLine(
      Offset(size.x * 0.15, size.y + 14),
      Offset(size.x * 0.85, size.y + 14),
      energyLinePaint,
    );
  }

  // رسم العيون الواقعية التفاعلية
  void _drawRealisticEyes(Canvas canvas) {
    final eyeWhitePaint = Paint()..color = Colors.white;
    final eyePupilPaint = Paint()..color = Colors.black;
    final eyeLidPaint = Paint()..color = const Color(0xFFFFDBB5); // لون البشرة

    // العين اليسرى
    final leftEyeCenter = Offset(size.x * 0.35, size.y * 0.25);

    if (_isBlinking) {
      // رسم العين مغلقة (خط)
      final eyeLidPath = Path();
      eyeLidPath.moveTo(leftEyeCenter.dx - 8, leftEyeCenter.dy);
      eyeLidPath.quadraticBezierTo(
        leftEyeCenter.dx, leftEyeCenter.dy - 2,
        leftEyeCenter.dx + 8, leftEyeCenter.dy,
      );
      canvas.drawPath(eyeLidPath, Paint()..color = Colors.black..strokeWidth = 2..style = PaintingStyle.stroke);
    } else {
      // رسم العين مفتوحة
      canvas.drawCircle(leftEyeCenter, 8, eyeWhitePaint);
      canvas.drawCircle(leftEyeCenter, 8, Paint()..color = Colors.black..strokeWidth = 1..style = PaintingStyle.stroke);

      // البؤبؤ يتبع اتجاه النظر
      final pupilOffset = Offset(
        _eyeDirection.x * 3,
        _eyeDirection.y * 3,
      );
      canvas.drawCircle(
        Offset(leftEyeCenter.dx + pupilOffset.dx, leftEyeCenter.dy + pupilOffset.dy),
        4,
        eyePupilPaint,
      );

      // نقطة الضوء في العين
      canvas.drawCircle(
        Offset(leftEyeCenter.dx + pupilOffset.dx + 1, leftEyeCenter.dy + pupilOffset.dy - 1),
        1.5,
        Paint()..color = Colors.white,
      );
    }

    // العين اليمنى
    final rightEyeCenter = Offset(size.x * 0.65, size.y * 0.25);

    if (_isBlinking) {
      // رسم العين مغلقة (خط)
      final eyeLidPath = Path();
      eyeLidPath.moveTo(rightEyeCenter.dx - 8, rightEyeCenter.dy);
      eyeLidPath.quadraticBezierTo(
        rightEyeCenter.dx, rightEyeCenter.dy - 2,
        rightEyeCenter.dx + 8, rightEyeCenter.dy,
      );
      canvas.drawPath(eyeLidPath, Paint()..color = Colors.black..strokeWidth = 2..style = PaintingStyle.stroke);
    } else {
      // رسم العين مفتوحة
      canvas.drawCircle(rightEyeCenter, 8, eyeWhitePaint);
      canvas.drawCircle(rightEyeCenter, 8, Paint()..color = Colors.black..strokeWidth = 1..style = PaintingStyle.stroke);

      // البؤبؤ يتبع اتجاه النظر
      final pupilOffset = Offset(
        _eyeDirection.x * 3,
        _eyeDirection.y * 3,
      );
      canvas.drawCircle(
        Offset(rightEyeCenter.dx + pupilOffset.dx, rightEyeCenter.dy + pupilOffset.dy),
        4,
        eyePupilPaint,
      );

      // نقطة الضوء في العين
      canvas.drawCircle(
        Offset(rightEyeCenter.dx + pupilOffset.dx + 1, rightEyeCenter.dy + pupilOffset.dy - 1),
        1.5,
        Paint()..color = Colors.white,
      );
    }

    // الحواجب التعبيرية
    final eyebrowPaint = Paint()
      ..color = const Color(0xFF8B4513) // بني
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    // الحاجب الأيسر
    final leftBrowY = leftEyeCenter.dy - 12 + (_isCrashed ? 3 : 0);
    canvas.drawLine(
      Offset(leftEyeCenter.dx - 10, leftBrowY),
      Offset(leftEyeCenter.dx + 10, leftBrowY - (_isCrashed ? 3 : 0)),
      eyebrowPaint,
    );

    // الحاجب الأيمن
    final rightBrowY = rightEyeCenter.dy - 12 + (_isCrashed ? 3 : 0);
    canvas.drawLine(
      Offset(rightEyeCenter.dx - 10, rightBrowY - (_isCrashed ? 3 : 0)),
      Offset(rightEyeCenter.dx + 10, rightBrowY),
      eyebrowPaint,
    );
  }

  // رسم الفم الواقعي التفاعلي
  void _drawRealisticMouth(Canvas canvas) {
    final mouthPaint = Paint()
      ..color = const Color(0xFFFF69B4) // وردي
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    final mouthPath = Path();
    final mouthCenterX = size.x * 0.5;
    final mouthY = size.y * 0.38;

    if (_mouthState > 0.6) {
      // ابتسامة سعيدة
      mouthPath.moveTo(mouthCenterX - 12, mouthY);
      mouthPath.quadraticBezierTo(
        mouthCenterX, mouthY + 8,
        mouthCenterX + 12, mouthY,
      );

      // أسنان بيضاء
      final teethPaint = Paint()..color = Colors.white;
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(mouthCenterX - 8, mouthY + 2, 16, 4),
          const Radius.circular(2),
        ),
        teethPaint,
      );

    } else if (_mouthState > 0.3) {
      // فم عادي
      mouthPath.moveTo(mouthCenterX - 8, mouthY);
      mouthPath.quadraticBezierTo(
        mouthCenterX, mouthY + 2,
        mouthCenterX + 8, mouthY,
      );

    } else {
      // فم حزين
      mouthPath.moveTo(mouthCenterX - 10, mouthY + 3);
      mouthPath.quadraticBezierTo(
        mouthCenterX, mouthY - 3,
        mouthCenterX + 10, mouthY + 3,
      );
    }

    canvas.drawPath(mouthPath, mouthPaint);

    // إضافة خدود وردية عند السعادة
    if (_mouthState > 0.7) {
      final cheekPaint = Paint()..color = const Color(0xFFFFB6C1).withAlpha(100);
      canvas.drawCircle(Offset(size.x * 0.25, size.y * 0.35), 8, cheekPaint);
      canvas.drawCircle(Offset(size.x * 0.75, size.y * 0.35), 8, cheekPaint);
    }
  }
}

// Professional Background Component with Realistic Environments
class ProfessionalBackground extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  late List<CloudComponent> clouds;
  late List<BuildingComponent> buildings;
  late LevelType currentLevelType;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    size = gameRef.size;
    position = Vector2.zero();
    currentLevelType = gameRef._currentLevelType;

    // Initialize environment elements based on level type
    _initializeEnvironment();
  }

  void _initializeEnvironment() {
    try {
      clouds = [];
      buildings = [];

      switch (currentLevelType) {
        case LevelType.city:
          _initializeCityEnvironment();
          break;
        case LevelType.desert:
          _initializeDesertEnvironment();
          break;
        case LevelType.forest:
          _initializeForestEnvironment();
          break;
        case LevelType.mountain:
          _initializeMountainEnvironment();
          break;
        case LevelType.ocean:
          _initializeOceanEnvironment();
          break;
        case LevelType.space:
          _initializeSpaceEnvironment();
          break;
        default:
          _initializeCityEnvironment();
      }
    } catch (e) {
      print('خطأ في إنشاء البيئة: $e');
      // في حالة الخطأ، إنشاء بيئة افتراضية بسيطة
      clouds = [];
      buildings = [];
      _initializeCityEnvironment();
    }
  }

  void _initializeCityEnvironment() {
    // Initialize clouds
    clouds = List.generate(5, (index) => CloudComponent(
      position: Vector2(
        index * 150.0 + Random().nextDouble() * 100,
        50.0 + Random().nextDouble() * 100,
      ),
      speed: 0.3 + Random().nextDouble() * 0.2,
    ));

    // Initialize buildings
    buildings = List.generate(8, (index) => BuildingComponent(
      position: Vector2(
        index * 80.0 + Random().nextDouble() * 50,
        size.y - 200 - Random().nextDouble() * 100,
      ),
      height: 100.0 + Random().nextDouble() * 150,
      speed: 0.5 + Random().nextDouble() * 0.3,
    ));
  }

  void _initializeDesertEnvironment() {
    // Desert has fewer clouds, more sand dunes
    clouds = List.generate(2, (index) => CloudComponent(
      position: Vector2(
        index * 300.0 + Random().nextDouble() * 200,
        30.0 + Random().nextDouble() * 50,
      ),
      speed: 0.1 + Random().nextDouble() * 0.1,
    ));
  }

  void _initializeForestEnvironment() {
    // Forest has many clouds and trees
    clouds = List.generate(7, (index) => CloudComponent(
      position: Vector2(
        index * 120.0 + Random().nextDouble() * 80,
        40.0 + Random().nextDouble() * 80,
      ),
      speed: 0.4 + Random().nextDouble() * 0.3,
    ));
  }

  void _initializeMountainEnvironment() {
    // Mountain has dramatic clouds
    clouds = List.generate(4, (index) => CloudComponent(
      position: Vector2(
        index * 200.0 + Random().nextDouble() * 150,
        20.0 + Random().nextDouble() * 60,
      ),
      speed: 0.2 + Random().nextDouble() * 0.2,
    ));
  }

  void _initializeOceanEnvironment() {
    // Ocean has many moving clouds
    clouds = List.generate(6, (index) => CloudComponent(
      position: Vector2(
        index * 140.0 + Random().nextDouble() * 100,
        30.0 + Random().nextDouble() * 70,
      ),
      speed: 0.5 + Random().nextDouble() * 0.4,
    ));
  }

  void _initializeSpaceEnvironment() {
    // Space has no clouds, but stars
    clouds = [];
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Update clouds
    for (final cloud in clouds) {
      cloud.update(dt, gameRef.gameSpeed);
      if (cloud.position.x > size.x + 100) {
        cloud.position.x = -100;
      }
    }

    // Update buildings
    for (final building in buildings) {
      building.update(dt, gameRef.gameSpeed);
      if (building.position.x > size.x + 100) {
        building.position.x = -100;
      }
    }
  }

  @override
  void render(Canvas canvas) {
    // رسم الخلفية حسب نوع المستوى
    switch (currentLevelType) {
      case LevelType.city:
        _drawCityBackground(canvas);
        break;
      case LevelType.desert:
        _drawDesertBackground(canvas);
        break;
      case LevelType.forest:
        _drawForestBackground(canvas);
        break;
      case LevelType.mountain:
        _drawMountainBackground(canvas);
        break;
      case LevelType.ocean:
        _drawOceanBackground(canvas);
        break;
      case LevelType.space:
        _drawSpaceBackground(canvas);
        break;
      case LevelType.volcano:
        _drawVolcanoBackground(canvas);
        break;
      case LevelType.ice:
        _drawIceBackground(canvas);
        break;
      case LevelType.jungle:
        _drawJungleBackground(canvas);
        break;
      case LevelType.cave:
        _drawCaveBackground(canvas);
        break;
      default:
        _drawCityBackground(canvas);
    }

    // رسم العناصر المشتركة
    _drawClouds(canvas);
    _drawEnvironmentDetails(canvas);
  }

  void _drawCityBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF1E3A8A), // أزرق داكن
        const Color(0xFF3B82F6), // أزرق متوسط
        const Color(0xFF60A5FA), // أزرق فاتح
        const Color(0xFF93C5FD), // أزرق فاتح جداً
        const Color(0xFFDDD6FE), // بنفسجي فاتح
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم المباني
    _drawCityBuildings(canvas);
  }

  void _drawDesertBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFFFFE4B5), // بيج فاتح
        const Color(0xFFDEB887), // بيج متوسط
        const Color(0xFFD2B48C), // بيج داكن
        const Color(0xFFF4A460), // رملي
        const Color(0xFFCD853F), // بني رملي
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم الكثبان الرملية
    _drawSandDunes(canvas);
    // رسم الصبار
    _drawCactus(canvas);
  }

  void _drawForestBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF228B22), // أخضر غابة
        const Color(0xFF32CD32), // أخضر فاتح
        const Color(0xFF90EE90), // أخضر فاتح جداً
        const Color(0xFF98FB98), // أخضر نعناعي
        const Color(0xFFADFF2F), // أخضر مصفر
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم الأشجار
    _drawTrees(canvas);
  }

  void _drawMountainBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF4682B4), // أزرق فولاذي
        const Color(0xFF87CEEB), // أزرق سماوي
        const Color(0xFFB0C4DE), // أزرق فاتح
        const Color(0xFFD3D3D3), // رمادي فاتح
        const Color(0xFFA9A9A9), // رمادي داكن
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم الجبال
    _drawMountainPeaks(canvas);
  }

  void _drawOceanBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF00BFFF), // أزرق سماوي عميق
        const Color(0xFF1E90FF), // أزرق دودج
        const Color(0xFF4169E1), // أزرق ملكي
        const Color(0xFF0000CD), // أزرق متوسط
        const Color(0xFF000080), // أزرق بحري
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم الأمواج
    _drawOceanWaves(canvas);
  }

  void _drawSpaceBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = RadialGradient(
      center: Alignment.center,
      radius: 1.0,
      colors: [
        const Color(0xFF191970), // أزرق منتصف الليل
        const Color(0xFF000000), // أسود
        const Color(0xFF2F4F4F), // رمادي أردوازي داكن
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم النجوم
    _drawStars(canvas);
    // رسم الكواكب
    _drawPlanets(canvas);
  }

  void _drawVolcanoBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF8B0000), // أحمر داكن
        const Color(0xFFDC143C), // قرمزي
        const Color(0xFFFF4500), // أحمر برتقالي
        const Color(0xFFFF6347), // طماطم
        const Color(0xFFFF8C00), // برتقالي داكن
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم البركان
    _drawVolcano(canvas);
    // رسم الحمم
    _drawLava(canvas);
  }

  void _drawIceBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFFE0FFFF), // أزرق فاتح جداً
        const Color(0xFFB0E0E6), // أزرق مسحوق
        const Color(0xFF87CEFA), // أزرق سماوي فاتح
        const Color(0xFF87CEEB), // أزرق سماوي
        const Color(0xFFADD8E6), // أزرق فاتح
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم الجليد
    _drawIceBergs(canvas);
  }

  void _drawJungleBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF006400), // أخضر داكن
        const Color(0xFF228B22), // أخضر غابة
        const Color(0xFF32CD32), // أخضر ليموني
        const Color(0xFF9ACD32), // أخضر مصفر
        const Color(0xFF7CFC00), // أخضر عشبي
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم النباتات الاستوائية
    _drawJunglePlants(canvas);
  }

  void _drawCaveBackground(Canvas canvas) {
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF2F2F2F), // رمادي داكن
        const Color(0xFF1C1C1C), // رمادي أغمق
        const Color(0xFF000000), // أسود
        const Color(0xFF2F4F4F), // رمادي أردوازي
        const Color(0xFF696969), // رمادي خافت
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // رسم الصواعد والهوابط
    _drawCaveFormations(canvas);
  }

  // دوال الرسم المساعدة
  void _drawClouds(Canvas canvas) {
    for (final cloud in clouds) {
      cloud.render(canvas);
    }
  }

  void _drawEnvironmentDetails(Canvas canvas) {
    // رسم تفاصيل إضافية حسب البيئة
    switch (currentLevelType) {
      case LevelType.city:
        _drawFlyingBirds(canvas);
        break;
      case LevelType.desert:
        _drawSandStorm(canvas);
        break;
      case LevelType.forest:
        _drawFallingLeaves(canvas);
        break;
      case LevelType.ocean:
        _drawSeagulls(canvas);
        break;
      case LevelType.space:
        _drawMeteors(canvas);
        break;
      default:
        break;
    }
  }

  void _drawCityBuildings(Canvas canvas) {
    for (final building in buildings) {
      building.render(canvas);
    }
  }

  void _drawSandDunes(Canvas canvas) {
    final dunePaint = Paint()..color = const Color(0xFFF4A460);

    for (int i = 0; i < 5; i++) {
      final duneX = i * 200.0 - (DateTime.now().millisecondsSinceEpoch / 50) % 1000;
      final duneY = size.y - 100 - (i * 20);

      final dunePath = Path();
      dunePath.moveTo(duneX, size.y);
      dunePath.quadraticBezierTo(duneX + 100, duneY, duneX + 200, size.y);
      dunePath.close();

      canvas.drawPath(dunePath, dunePaint);
    }
  }

  void _drawCactus(Canvas canvas) {
    final cactusPaint = Paint()..color = const Color(0xFF228B22);

    for (int i = 0; i < 3; i++) {
      final cactusX = i * 300.0 + 100 - (DateTime.now().millisecondsSinceEpoch / 100) % 900;
      final cactusY = size.y - 150;

      // جذع الصبار
      canvas.drawRect(
        Rect.fromLTWH(cactusX, cactusY, 20, 80),
        cactusPaint,
      );

      // أذرع الصبار
      canvas.drawRect(
        Rect.fromLTWH(cactusX - 15, cactusY + 20, 15, 40),
        cactusPaint,
      );
      canvas.drawRect(
        Rect.fromLTWH(cactusX + 20, cactusY + 30, 15, 30),
        cactusPaint,
      );
    }
  }

  void _drawTrees(Canvas canvas) {
    final trunkPaint = Paint()..color = const Color(0xFF8B4513);
    final leavesPaint = Paint()..color = const Color(0xFF228B22);

    for (int i = 0; i < 6; i++) {
      final treeX = i * 150.0 + 50 - (DateTime.now().millisecondsSinceEpoch / 80) % 900;
      final treeY = size.y - 200;

      // جذع الشجرة
      canvas.drawRect(
        Rect.fromLTWH(treeX, treeY, 15, 100),
        trunkPaint,
      );

      // أوراق الشجرة
      canvas.drawCircle(
        Offset(treeX + 7.5, treeY - 20),
        40,
        leavesPaint,
      );
    }
  }

  void _drawMountainPeaks(Canvas canvas) {
    final mountainPaint = Paint()..color = const Color(0xFF696969);

    for (int i = 0; i < 4; i++) {
      final mountainX = i * 250.0 - (DateTime.now().millisecondsSinceEpoch / 30) % 1000;
      final mountainY = size.y - 200;
      final height = 150 + (i * 30);

      final mountainPath = Path();
      mountainPath.moveTo(mountainX, size.y);
      mountainPath.lineTo(mountainX + 125, mountainY - height);
      mountainPath.lineTo(mountainX + 250, size.y);
      mountainPath.close();

      canvas.drawPath(mountainPath, mountainPaint);

      // قمة ثلجية
      final snowPaint = Paint()..color = Colors.white;
      final snowPath = Path();
      snowPath.moveTo(mountainX + 100, mountainY - height + 30);
      snowPath.lineTo(mountainX + 125, mountainY - height);
      snowPath.lineTo(mountainX + 150, mountainY - height + 30);
      snowPath.close();

      canvas.drawPath(snowPath, snowPaint);
    }
  }

  void _drawOceanWaves(Canvas canvas) {
    final wavePaint = Paint()
      ..color = Colors.white.withAlpha(100)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    final time = DateTime.now().millisecondsSinceEpoch / 500;

    for (int i = 0; i < 5; i++) {
      final waveY = size.y - 50 - (i * 20);
      final wavePath = Path();

      wavePath.moveTo(0, waveY);
      for (double x = 0; x <= size.x; x += 20) {
        final y = waveY + sin((x / 50) + time + (i * 0.5)) * 10;
        wavePath.lineTo(x, y);
      }

      canvas.drawPath(wavePath, wavePaint);
    }
  }

  void _drawStars(Canvas canvas) {
    final starPaint = Paint()..color = Colors.white;
    final random = Random(42); // seed ثابت للنجوم

    for (int i = 0; i < 100; i++) {
      final x = random.nextDouble() * size.x;
      final y = random.nextDouble() * size.y * 0.7;
      final brightness = random.nextDouble();

      starPaint.color = Colors.white.withOpacity(brightness);
      canvas.drawCircle(Offset(x, y), 1 + brightness, starPaint);
    }
  }

  void _drawPlanets(Canvas canvas) {
    final planetPaint = Paint();

    // كوكب كبير
    planetPaint.color = const Color(0xFFFF6347);
    canvas.drawCircle(
      Offset(size.x * 0.8, size.y * 0.2),
      30,
      planetPaint,
    );

    // كوكب صغير
    planetPaint.color = const Color(0xFF4169E1);
    canvas.drawCircle(
      Offset(size.x * 0.2, size.y * 0.3),
      15,
      planetPaint,
    );
  }

  void _drawVolcano(Canvas canvas) {
    final volcanoPath = Path();
    final volcanoX = size.x * 0.7;
    final volcanoY = size.y - 100;

    volcanoPath.moveTo(volcanoX - 100, size.y);
    volcanoPath.lineTo(volcanoX - 30, volcanoY);
    volcanoPath.lineTo(volcanoX + 30, volcanoY);
    volcanoPath.lineTo(volcanoX + 100, size.y);
    volcanoPath.close();

    final volcanoP = Paint()..color = const Color(0xFF8B4513);
    canvas.drawPath(volcanoPath, volcanoP);
  }

  void _drawLava(Canvas canvas) {
    final lavaPaint = Paint()..color = const Color(0xFFFF4500);
    final time = DateTime.now().millisecondsSinceEpoch / 200;

    for (int i = 0; i < 3; i++) {
      final x = size.x * 0.7 + sin(time + i) * 10;
      final y = size.y - 100 + i * 20;
      canvas.drawCircle(Offset(x, y), 5 + sin(time + i) * 2, lavaPaint);
    }
  }

  void _drawIceBergs(Canvas canvas) {
    final icePaint = Paint()..color = const Color(0xFFE0FFFF);

    for (int i = 0; i < 3; i++) {
      final iceX = i * 200.0 + 100 - (DateTime.now().millisecondsSinceEpoch / 60) % 600;
      final iceY = size.y - 80;

      final icePath = Path();
      icePath.moveTo(iceX, size.y);
      icePath.lineTo(iceX + 50, iceY);
      icePath.lineTo(iceX + 100, size.y);
      icePath.close();

      canvas.drawPath(icePath, icePaint);
    }
  }

  void _drawJunglePlants(Canvas canvas) {
    final plantPaint = Paint()..color = const Color(0xFF228B22);

    for (int i = 0; i < 8; i++) {
      final plantX = i * 100.0 + 50 - (DateTime.now().millisecondsSinceEpoch / 90) % 800;
      final plantY = size.y - 120;

      // نبات استوائي
      canvas.drawRect(
        Rect.fromLTWH(plantX, plantY, 10, 60),
        plantPaint,
      );

      // أوراق
      for (int j = 0; j < 3; j++) {
        canvas.drawCircle(
          Offset(plantX + 5, plantY + j * 20),
          15,
          plantPaint,
        );
      }
    }
  }

  void _drawCaveFormations(Canvas canvas) {
    final stalactitePaint = Paint()..color = const Color(0xFF696969);

    // صواعد (من الأعلى)
    for (int i = 0; i < 5; i++) {
      final x = i * 150.0 + 75;
      final height = 50 + (i * 20);

      final stalactitePath = Path();
      stalactitePath.moveTo(x - 10, 0);
      stalactitePath.lineTo(x, height.toDouble());
      stalactitePath.lineTo(x + 10, 0);
      stalactitePath.close();

      canvas.drawPath(stalactitePath, stalactitePaint);
    }

    // هوابط (من الأسفل)
    for (int i = 0; i < 4; i++) {
      final x = i * 180.0 + 150;
      final height = 40 + (i * 15);

      final stalagmitePath = Path();
      stalagmitePath.moveTo(x - 8, size.y);
      stalagmitePath.lineTo(x, size.y - height);
      stalagmitePath.lineTo(x + 8, size.y);
      stalagmitePath.close();

      canvas.drawPath(stalagmitePath, stalactitePaint);
    }
  }

  void _drawSandStorm(Canvas canvas) {
    final sandPaint = Paint()
      ..color = const Color(0xFFF4A460).withAlpha(100)
      ..style = PaintingStyle.fill;

    final time = DateTime.now().millisecondsSinceEpoch / 100;

    for (int i = 0; i < 20; i++) {
      final x = (i * 50.0 + time * 2) % size.x;
      final y = (i * 30.0 + sin(time + i) * 50) % size.y;
      canvas.drawCircle(Offset(x, y), 2 + sin(time + i), sandPaint);
    }
  }

  void _drawFallingLeaves(Canvas canvas) {
    final leafPaint = Paint()..color = const Color(0xFF228B22);
    final time = DateTime.now().millisecondsSinceEpoch / 300;

    for (int i = 0; i < 10; i++) {
      final x = (i * 80.0 + sin(time + i) * 30) % size.x;
      final y = (time * 50 + i * 60) % size.y;

      canvas.drawCircle(Offset(x, y), 3, leafPaint);
    }
  }

  void _drawSeagulls(Canvas canvas) {
    final birdPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final time = DateTime.now().millisecondsSinceEpoch / 1000;

    for (int i = 0; i < 3; i++) {
      final x = (i * 200.0 + time * 100) % size.x;
      final y = 50 + sin(time + i) * 30;

      // رسم طائر بسيط
      final birdPath = Path();
      birdPath.moveTo(x - 10, y);
      birdPath.quadraticBezierTo(x, y - 5, x + 10, y);
      birdPath.moveTo(x - 5, y);
      birdPath.quadraticBezierTo(x, y + 3, x + 5, y);

      canvas.drawPath(birdPath, birdPaint);
    }
  }

  void _drawMeteors(Canvas canvas) {
    final meteorPaint = Paint()..color = const Color(0xFFFF6347);
    final time = DateTime.now().millisecondsSinceEpoch / 200;

    for (int i = 0; i < 5; i++) {
      final x = (i * 150.0 + time * 3) % size.x;
      final y = (i * 100.0 + time * 2) % size.y;

      canvas.drawCircle(Offset(x, y), 3, meteorPaint);

      // ذيل النيزك
      final tailPaint = Paint()
        ..color = const Color(0xFFFF6347).withAlpha(100)
        ..strokeWidth = 2;

      canvas.drawLine(
        Offset(x, y),
        Offset(x - 20, y - 10),
        tailPaint,
      );
    }
  }

  void _drawFlyingBirds(Canvas canvas) {
    final birdPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    final time = DateTime.now().millisecondsSinceEpoch / 2000;

    for (int i = 0; i < 4; i++) {
      final x = (i * 180.0 + time * 80) % size.x;
      final y = 80 + sin(time + i) * 40;

      // رسم طائر
      final birdPath = Path();
      birdPath.moveTo(x - 8, y);
      birdPath.quadraticBezierTo(x, y - 4, x + 8, y);
      birdPath.moveTo(x - 4, y);
      birdPath.quadraticBezierTo(x, y + 2, x + 4, y);

      canvas.drawPath(birdPath, birdPaint);
    }
  }

  void _drawMountains(Canvas canvas) {
    final mountainPaint = Paint()
      ..color = const Color(0xFF4B5563).withAlpha(100)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 5; i++) {
      final mountainPath = Path();
      final baseX = i * 200.0 - (DateTime.now().millisecondsSinceEpoch / 100) % 1000;
      final baseY = size.y - 100;
      final height = 80 + (i * 20);

      mountainPath.moveTo(baseX, baseY);
      mountainPath.lineTo(baseX + 100, baseY - height);
      mountainPath.lineTo(baseX + 200, baseY);
      mountainPath.close();

      canvas.drawPath(mountainPath, mountainPaint);
    }
  }

  void _drawRealisticClouds(Canvas canvas) {
    final time = DateTime.now().millisecondsSinceEpoch / 1000;

    for (int i = 0; i < 8; i++) {
      final cloudX = (i * 150.0 - time * 20) % (size.x + 200) - 100;
      final cloudY = 50 + (i % 3) * 80 + sin(time + i) * 10;

      _drawSingleCloud(canvas, cloudX, cloudY, 0.8 + (i % 3) * 0.3);
    }
  }

  void _drawSingleCloud(Canvas canvas, double x, double y, double scale) {
    final cloudPaint = Paint()
      ..color = Colors.white.withAlpha(200)
      ..style = PaintingStyle.fill;

    // سحابة متعددة الطبقات
    canvas.drawCircle(Offset(x, y), 25 * scale, cloudPaint);
    canvas.drawCircle(Offset(x + 30 * scale, y), 35 * scale, cloudPaint);
    canvas.drawCircle(Offset(x + 60 * scale, y), 25 * scale, cloudPaint);
    canvas.drawCircle(Offset(x + 30 * scale, y - 20 * scale), 20 * scale, cloudPaint);
    canvas.drawCircle(Offset(x + 45 * scale, y - 15 * scale), 15 * scale, cloudPaint);
  }


}

// Helper classes for background elements
class CloudComponent {
  Vector2 position;
  final double speed;

  CloudComponent({required this.position, required this.speed});

  void update(double dt, double gameSpeed) {
    position.x += speed * gameSpeed * dt * 0.1;
  }

  void render(Canvas canvas) {
    final cloudPaint = Paint()..color = Colors.white.withOpacity(0.8);

    canvas.drawCircle(Offset(position.x, position.y), 25, cloudPaint);
    canvas.drawCircle(Offset(position.x + 20, position.y), 30, cloudPaint);
    canvas.drawCircle(Offset(position.x + 40, position.y), 25, cloudPaint);
    canvas.drawCircle(Offset(position.x + 20, position.y - 15), 20, cloudPaint);
  }
}

class BuildingComponent {
  Vector2 position;
  final double height;
  final double speed;

  BuildingComponent({
    required this.position,
    required this.height,
    required this.speed,
  });

  void update(double dt, double gameSpeed) {
    position.x += speed * gameSpeed * dt * 0.1;
  }

  void render(Canvas canvas) {
    final buildingPaint = Paint()..color = Colors.grey.withOpacity(0.4);

    canvas.drawRect(
      Rect.fromLTWH(position.x, position.y, 60, height),
      buildingPaint,
    );

    // Windows
    final windowPaint = Paint()..color = Colors.yellow.withOpacity(0.6);
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < (height / 30).floor(); j++) {
        if (Random().nextBool()) {
          canvas.drawRect(
            Rect.fromLTWH(
              position.x + 10 + i * 15,
              position.y + 10 + j * 25,
              8,
              12,
            ),
            windowPaint,
          );
        }
      }
    }
  }
}

// Professional Ground Component
class ProfessionalGround extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  late List<Vector2> trackMarks;
  double _scrollOffset = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    size = Vector2(gameRef.size.x, 50);
    position = Vector2(0, gameRef.size.y - 50);
    paint.color = Colors.brown.shade600;

    // Initialize track marks for moving effect
    trackMarks = List.generate(20, (index) => Vector2(
      index * 40.0,
      gameRef.size.y - 75,
    ));
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Update scroll offset for moving ground effect
    _scrollOffset += gameRef.gameSpeed * dt;

    // Reset offset to prevent overflow
    if (_scrollOffset > 40) {
      _scrollOffset -= 40;
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    // Draw track marks
    final markPaint = Paint()
      ..color = Colors.yellow
      ..strokeWidth = 3;

    for (int i = 0; i < trackMarks.length; i++) {
      final x = (trackMarks[i].x - _scrollOffset) % (size.x + 40);
      canvas.drawLine(
        Offset(x, trackMarks[i].y),
        Offset(x, trackMarks[i].y + 10),
        markPaint,
      );
    }
  }
}

// أنواع العوائق الجوية الجديدة
enum SkyObstacleType {
  meteor,        // نيزك
  thunderBird,   // طائر صاعق
  fireBird,      // طائر ناري
  drone,         // طائرة بدون طيار
  satellite,     // قمر صناعي
  balloon,       // منطاد
}

// أنواع العوائق الأرضية
enum GroundObstacleType {
  tank,          // دبابة
  truck,         // شاحنة
  train,         // قطار
  building,      // مبنى
  fence,         // سياج
  spikes,        // أشواك
  laser,         // شعاع ليزر
  robot,         // روبوت
}

// عوائق السماء الجديدة - فقط في الجو
class SkyObstacle extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double obstacleWidth = 60.0;
  static const double obstacleHeight = 60.0;

  late Color _color;
  late Timer _warningTimer;
  late Timer _animationTimer;
  bool _showWarning = false;
  late SkyObstacleType _type;
  double _animationFrame = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // اختيار نوع عائق جوي عشوائي
    final random = Random();
    _type = SkyObstacleType.values[random.nextInt(SkyObstacleType.values.length)];

    // تحديد الحجم واللون حسب النوع
    _setupSkyObstacleByType();

    add(RectangleHitbox());

    // Warning blink effect
    _warningTimer = Timer(
      0.3,
      repeat: true,
      onTick: () {
        _showWarning = !_showWarning;
      },
    );
    _warningTimer.start();

    // Animation timer for movement effects
    _animationTimer = Timer(
      0.1,
      repeat: true,
      onTick: () {
        _animationFrame += 0.1;
      },
    );
    _animationTimer.start();
  }

  void _setupSkyObstacleByType() {
    switch (_type) {
      case SkyObstacleType.meteor:
        size = Vector2(50, 50); // نيزك دائري
        _color = Colors.orange.shade800;
        break;
      case SkyObstacleType.thunderBird:
        size = Vector2(70, 40); // طائر صاعق
        _color = Colors.purple.shade600;
        break;
      case SkyObstacleType.fireBird:
        size = Vector2(65, 35); // طائر ناري
        _color = Colors.red.shade600;
        break;
      case SkyObstacleType.drone:
        size = Vector2(60, 30); // طائرة بدون طيار
        _color = Colors.grey.shade700;
        break;
      case SkyObstacleType.satellite:
        size = Vector2(80, 50); // قمر صناعي
        _color = Colors.blue.shade800;
        break;
      case SkyObstacleType.balloon:
        size = Vector2(45, 70); // منطاد
        _color = Colors.pink.shade400;
        break;
    }
    paint.color = Colors.transparent; // شفاف لأننا نرسم بشكل مخصص
  }

  @override
  void update(double dt) {
    super.update(dt);

    _warningTimer.update(dt);

    // Move obstacle to the left
    position.x -= gameRef.gameSpeed * dt;

    // Remove when off screen
    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    // رسم العائق الجوي حسب النوع
    switch (_type) {
      case SkyObstacleType.meteor:
        _drawMeteor(canvas);
        break;
      case SkyObstacleType.thunderBird:
        _drawThunderBird(canvas);
        break;
      case SkyObstacleType.fireBird:
        _drawFireBird(canvas);
        break;
      case SkyObstacleType.drone:
        _drawDrone(canvas);
        break;
      case SkyObstacleType.satellite:
        _drawSatellite(canvas);
        break;
      case SkyObstacleType.balloon:
        _drawBalloon(canvas);
        break;
    }

    // Warning triangle on top
    if (_showWarning) {
      _drawWarningSign(canvas);
    }
  }

  void _drawWoodenCrate(Canvas canvas) {
    // الجسم الرئيسي للصندوق
    final cratePaint = Paint()
      ..color = const Color(0xFF8B4513) // بني خشبي
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.x, size.y),
        const Radius.circular(4),
      ),
      cratePaint,
    );

    // خطوط الخشب
    final woodLinePaint = Paint()
      ..color = const Color(0xFF654321) // بني داكن
      ..strokeWidth = 2;

    for (int i = 1; i < 4; i++) {
      final y = size.y * i / 4;
      canvas.drawLine(
        Offset(2, y),
        Offset(size.x - 2, y),
        woodLinePaint,
      );
    }

    // خطوط عمودية
    for (int i = 1; i < 3; i++) {
      final x = size.x * i / 3;
      canvas.drawLine(
        Offset(x, 2),
        Offset(x, size.y - 2),
        woodLinePaint,
      );
    }

    // مسامير معدنية
    final nailPaint = Paint()
      ..color = const Color(0xFF708090) // رمادي معدني
      ..style = PaintingStyle.fill;

    // مسامير في الزوايا
    canvas.drawCircle(Offset(5, 5), 2, nailPaint);
    canvas.drawCircle(Offset(size.x - 5, 5), 2, nailPaint);
    canvas.drawCircle(Offset(5, size.y - 5), 2, nailPaint);
    canvas.drawCircle(Offset(size.x - 5, size.y - 5), 2, nailPaint);

    // ظل
    final shadowPaint = Paint()
      ..color = Colors.black.withAlpha(50)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(2, 2, size.x, size.y),
        const Radius.circular(4),
      ),
      shadowPaint,
    );
  }

  void _drawWarningSign(Canvas canvas) {
    final trianglePaint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;

    final trianglePath = Path();
    trianglePath.moveTo(size.x * 0.5, -20);
    trianglePath.lineTo(size.x * 0.2, -5);
    trianglePath.lineTo(size.x * 0.8, -5);
    trianglePath.close();
    canvas.drawPath(trianglePath, trianglePaint);

    // إطار المثلث
    final borderPaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawPath(trianglePath, borderPaint);

    // علامة التعجب
    final exclamationPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(size.x * 0.5, -16),
      Offset(size.x * 0.5, -10),
      exclamationPaint,
    );
    canvas.drawCircle(
      Offset(size.x * 0.5, -7),
      1.5,
      Paint()..color = Colors.red,
    );
  }

  // رسم طائرة
  void _drawAirplane(Canvas canvas) {
    // جسم الطائرة
    final bodyPaint = Paint()..color = Colors.blue.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, size.y * 0.3, size.x, size.y * 0.4),
        const Radius.circular(8),
      ),
      bodyPaint,
    );

    // الأجنحة
    final wingPaint = Paint()..color = Colors.blue.shade400;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.1, size.y * 0.5, size.x * 0.8, size.y * 0.2),
        const Radius.circular(4),
      ),
      wingPaint,
    );

    // المروحة
    final propellerPaint = Paint()..color = Colors.grey.shade600;
    canvas.drawCircle(
      Offset(size.x * 0.9, size.y * 0.5),
      8,
      propellerPaint,
    );

    // النوافذ
    final windowPaint = Paint()..color = Colors.lightBlue.shade200;
    for (int i = 0; i < 3; i++) {
      canvas.drawCircle(
        Offset(size.x * 0.2 + i * 15, size.y * 0.4),
        4,
        windowPaint,
      );
    }
  }

  // رسم صاروخ
  void _drawMissile(Canvas canvas) {
    // جسم الصاروخ
    final bodyPaint = Paint()..color = Colors.red.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.2, 0, size.x * 0.6, size.y * 0.8),
        const Radius.circular(6),
      ),
      bodyPaint,
    );

    // رأس الصاروخ (مثلث)
    final nosePaint = Paint()..color = Colors.red.shade800;
    final nosePath = Path();
    nosePath.moveTo(size.x * 0.5, 0);
    nosePath.lineTo(size.x * 0.2, size.y * 0.2);
    nosePath.lineTo(size.x * 0.8, size.y * 0.2);
    nosePath.close();
    canvas.drawPath(nosePath, nosePaint);

    // الزعانف
    final finPaint = Paint()..color = Colors.orange.shade600;
    final finPath = Path();
    finPath.moveTo(size.x * 0.1, size.y * 0.7);
    finPath.lineTo(size.x * 0.2, size.y * 0.9);
    finPath.lineTo(size.x * 0.8, size.y * 0.9);
    finPath.lineTo(size.x * 0.9, size.y * 0.7);
    finPath.close();
    canvas.drawPath(finPath, finPaint);

    // اللهب (تأثير الدفع)
    final flamePaint = Paint()..color = Colors.orange;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.3, size.y * 0.9, size.x * 0.4, size.y * 0.1),
        const Radius.circular(2),
      ),
      flamePaint,
    );
  }

  // رسم جاسوس
  void _drawSpy(Canvas canvas) {
    // الجسم
    final bodyPaint = Paint()..color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.2, size.y * 0.4, size.x * 0.6, size.y * 0.5),
        const Radius.circular(8),
      ),
      bodyPaint,
    );

    // الرأس
    final headPaint = Paint()..color = const Color(0xFFFFDBB5); // لون البشرة
    canvas.drawCircle(
      Offset(size.x * 0.5, size.y * 0.25),
      size.x * 0.2,
      headPaint,
    );

    // القبعة
    final hatPaint = Paint()..color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.3, size.y * 0.1, size.x * 0.4, size.y * 0.2),
        const Radius.circular(4),
      ),
      hatPaint,
    );

    // النظارات السوداء
    final glassesPaint = Paint()..color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.35, size.y * 0.22, size.x * 0.3, size.y * 0.08),
        const Radius.circular(2),
      ),
      glassesPaint,
    );

    // الأذرع
    final armPaint = Paint()..color = const Color(0xFFFFDBB5);
    canvas.drawCircle(Offset(size.x * 0.1, size.y * 0.6), 6, armPaint);
    canvas.drawCircle(Offset(size.x * 0.9, size.y * 0.6), 6, armPaint);

    // الأرجل
    final legPaint = Paint()..color = Colors.black;
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.35, size.y * 0.85, 6, size.y * 0.15),
      legPaint,
    );
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.55, size.y * 0.85, 6, size.y * 0.15),
      legPaint,
    );
  }

  // رسم نيزك
  void _drawMeteor(Canvas canvas) {
    // جسم النيزك الرئيسي
    final meteorPaint = Paint()
      ..shader = RadialGradient(
        colors: [Colors.orange, Colors.red, Colors.brown],
      ).createShader(Rect.fromCircle(center: Offset(size.x * 0.5, size.y * 0.5), radius: size.x * 0.4));

    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.5), size.x * 0.4, meteorPaint);

    // ذيل النيزك المتوهج
    final tailPaint = Paint()..color = Colors.orange.withAlpha(150);
    for (int i = 0; i < 5; i++) {
      canvas.drawCircle(
        Offset(size.x * 0.2 - i * 8, size.y * 0.7 + i * 5),
        8 - i * 1.5,
        tailPaint,
      );
    }

    // شرارات متطايرة
    final sparkPaint = Paint()..color = Colors.yellow;
    for (int i = 0; i < 6; i++) {
      final sparkX = size.x * 0.5 + sin(_animationFrame * 5 + i) * 20;
      final sparkY = size.y * 0.5 + cos(_animationFrame * 5 + i) * 20;
      canvas.drawCircle(Offset(sparkX, sparkY), 2, sparkPaint);
    }
  }

  // رسم طائر صاعق
  void _drawThunderBird(Canvas canvas) {
    // جسم الطائر
    final bodyPaint = Paint()..color = Colors.purple.shade600;
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.2, size.y * 0.3, size.x * 0.6, size.y * 0.4),
      bodyPaint,
    );

    // الأجنحة المتحركة
    final wingPaint = Paint()..color = Colors.purple.shade400;
    final wingOffset = sin(_animationFrame * 8) * 8;

    // الجناح الأيسر
    canvas.drawOval(
      Rect.fromLTWH(0, size.y * 0.2 + wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // الجناح الأيمن
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.7, size.y * 0.2 - wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // العيون المتوهجة
    final eyePaint = Paint()..color = Colors.yellow;
    canvas.drawCircle(Offset(size.x * 0.35, size.y * 0.4), 4, eyePaint);
    canvas.drawCircle(Offset(size.x * 0.65, size.y * 0.4), 4, eyePaint);

    // البرق حول الطائر
    final lightningPaint = Paint()
      ..color = Colors.yellow
      ..strokeWidth = 3;

    for (int i = 0; i < 4; i++) {
      final startX = size.x * 0.5 + sin(_animationFrame * 6 + i) * 25;
      final startY = size.y * 0.5 + cos(_animationFrame * 6 + i) * 20;
      canvas.drawLine(
        Offset(startX, startY),
        Offset(startX + 15, startY + 20),
        lightningPaint,
      );
    }
  }

  // رسم طائر ناري
  void _drawFireBird(Canvas canvas) {
    // جسم الطائر الناري
    final bodyPaint = Paint()
      ..shader = RadialGradient(
        colors: [Colors.red, Colors.orange, Colors.yellow],
      ).createShader(Rect.fromLTWH(0, 0, size.x, size.y));

    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.2, size.y * 0.3, size.x * 0.6, size.y * 0.4),
      bodyPaint,
    );

    // الأجنحة النارية
    final wingPaint = Paint()..color = Colors.orange.shade600;
    final wingOffset = sin(_animationFrame * 10) * 6;

    canvas.drawOval(
      Rect.fromLTWH(0, size.y * 0.2 + wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.7, size.y * 0.2 - wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // اللهب حول الطائر
    final flamePaint = Paint()..color = Colors.red.withAlpha(180);
    for (int i = 0; i < 8; i++) {
      final flameX = size.x * 0.5 + sin(_animationFrame * 4 + i) * 18;
      final flameY = size.y * 0.5 + cos(_animationFrame * 4 + i) * 15;
      canvas.drawCircle(Offset(flameX, flameY), 4, flamePaint);
    }
  }

  // رسم طائرة بدون طيار
  void _drawDrone(Canvas canvas) {
    // جسم الطائرة
    final bodyPaint = Paint()..color = Colors.grey.shade700;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.3, size.y * 0.4, size.x * 0.4, size.y * 0.2),
        const Radius.circular(4),
      ),
      bodyPaint,
    );

    // المراوح
    final propellerPaint = Paint()..color = Colors.black;
    final propellerRotation = _animationFrame * 20;

    for (int i = 0; i < 4; i++) {
      final propX = size.x * (0.2 + i * 0.2);
      final propY = size.y * 0.3;

      canvas.save();
      canvas.translate(propX, propY);
      canvas.rotate(propellerRotation);

      canvas.drawLine(const Offset(-8, 0), const Offset(8, 0), propellerPaint);
      canvas.drawLine(const Offset(0, -8), const Offset(0, 8), propellerPaint);

      canvas.restore();
    }

    // كاميرا
    final cameraPaint = Paint()..color = Colors.red;
    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.6), 3, cameraPaint);
  }

  // رسم قمر صناعي
  void _drawSatellite(Canvas canvas) {
    // الجسم الرئيسي
    final bodyPaint = Paint()..color = Colors.blue.shade800;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.3, size.y * 0.3, size.x * 0.4, size.y * 0.4),
        const Radius.circular(6),
      ),
      bodyPaint,
    );

    // الألواح الشمسية
    final solarPaint = Paint()..color = Colors.blue.shade400;
    canvas.drawRect(
      Rect.fromLTWH(0, size.y * 0.4, size.x * 0.25, size.y * 0.2),
      solarPaint,
    );
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.75, size.y * 0.4, size.x * 0.25, size.y * 0.2),
      solarPaint,
    );

    // الهوائي
    final antennaPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2;
    canvas.drawLine(
      Offset(size.x * 0.5, size.y * 0.3),
      Offset(size.x * 0.5, size.y * 0.1),
      antennaPaint,
    );

    // أضواء وامضة
    final lightPaint = Paint()..color = Colors.red;
    if ((_animationFrame * 10).toInt() % 2 == 0) {
      canvas.drawCircle(Offset(size.x * 0.4, size.y * 0.4), 2, lightPaint);
      canvas.drawCircle(Offset(size.x * 0.6, size.y * 0.6), 2, lightPaint);
    }
  }

  // رسم منطاد
  void _drawBalloon(Canvas canvas) {
    // البالون الرئيسي
    final balloonPaint = Paint()
      ..shader = LinearGradient(
        colors: [Colors.pink.shade200, Colors.pink.shade600],
      ).createShader(Rect.fromLTWH(0, 0, size.x, size.y * 0.7));

    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.1, 0, size.x * 0.8, size.y * 0.7),
      balloonPaint,
    );

    // السلة
    final basketPaint = Paint()..color = Colors.brown.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.35, size.y * 0.8, size.x * 0.3, size.y * 0.15),
        const Radius.circular(2),
      ),
      basketPaint,
    );

    // الحبال
    final ropePaint = Paint()
      ..color = Colors.brown
      ..strokeWidth = 1;

    for (int i = 0; i < 4; i++) {
      final ropeX = size.x * (0.35 + i * 0.1);
      canvas.drawLine(
        Offset(ropeX, size.y * 0.7),
        Offset(ropeX, size.y * 0.8),
        ropePaint,
      );
    }

    // تأثير الهواء الساخن
    final hotAirPaint = Paint()..color = Colors.orange.withAlpha(100);
    for (int i = 0; i < 3; i++) {
      canvas.drawCircle(
        Offset(size.x * 0.5, size.y * 0.75 + sin(_animationFrame * 3 + i) * 3),
        (3 - i).toDouble(),
        hotAirPaint,
      );
    }
  }
}

// العوائق الأرضية المتنوعة
class GroundObstacle extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double obstacleWidth = 80.0;
  static const double obstacleHeight = 100.0;

  late Color _color;
  late Timer _animationTimer;
  late GroundObstacleType _type;
  double _animationFrame = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // اختيار نوع عائق أرضي عشوائي
    final random = Random();
    _type = GroundObstacleType.values[random.nextInt(GroundObstacleType.values.length)];

    // تحديد الحجم واللون حسب النوع
    _setupGroundObstacleByType();

    add(RectangleHitbox());

    // Animation timer for movement effects
    _animationTimer = Timer(
      0.1,
      repeat: true,
      onTick: () {
        _animationFrame += 0.1;
      },
    );
    _animationTimer.start();
  }

  void _setupGroundObstacleByType() {
    switch (_type) {
      case GroundObstacleType.tank:
        size = Vector2(120, 60); // دبابة عريضة
        _color = Colors.green.shade800;
        break;
      case GroundObstacleType.truck:
        size = Vector2(100, 80); // شاحنة
        _color = Colors.blue.shade600;
        break;
      case GroundObstacleType.train:
        size = Vector2(150, 70); // قطار طويل
        _color = Colors.grey.shade700;
        break;
      case GroundObstacleType.building:
        size = Vector2(80, 120); // مبنى عالي
        _color = Colors.brown.shade600;
        break;
      case GroundObstacleType.fence:
        size = Vector2(60, 90); // سياج
        _color = Colors.brown.shade400;
        break;
      case GroundObstacleType.spikes:
        size = Vector2(70, 40); // أشواك منخفضة
        _color = Colors.grey.shade800;
        break;
      case GroundObstacleType.laser:
        size = Vector2(20, 100); // شعاع ليزر رفيع
        _color = Colors.red.shade600;
        break;
      case GroundObstacleType.robot:
        size = Vector2(60, 90); // روبوت
        _color = Colors.grey.shade600;
        break;
    }
    paint.color = Colors.transparent; // شفاف لأننا نرسم بشكل مخصص
  }

  @override
  void update(double dt) {
    super.update(dt);

    _animationTimer.update(dt);

    // Move obstacle to the left
    position.x -= gameRef.gameSpeed * dt;

    // Remove when off screen
    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    // رسم العائق الأرضي حسب النوع
    switch (_type) {
      case GroundObstacleType.tank:
        _drawTank(canvas);
        break;
      case GroundObstacleType.truck:
        _drawTruck(canvas);
        break;
      case GroundObstacleType.train:
        _drawTrain(canvas);
        break;
      case GroundObstacleType.building:
        _drawBuilding(canvas);
        break;
      case GroundObstacleType.fence:
        _drawFence(canvas);
        break;
      case GroundObstacleType.spikes:
        _drawSpikes(canvas);
        break;
      case GroundObstacleType.laser:
        _drawLaser(canvas);
        break;
      case GroundObstacleType.robot:
        _drawRobot(canvas);
        break;
    }
  }

  // رسم دبابة
  void _drawTank(Canvas canvas) {
    // جسم الدبابة
    final bodyPaint = Paint()..color = Colors.green.shade800;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, size.y * 0.4, size.x, size.y * 0.4),
        const Radius.circular(8),
      ),
      bodyPaint,
    );

    // البرج
    final turretPaint = Paint()..color = Colors.green.shade700;
    canvas.drawCircle(
      Offset(size.x * 0.6, size.y * 0.5),
      size.y * 0.15,
      turretPaint,
    );

    // المدفع
    final cannonPaint = Paint()
      ..color = Colors.grey.shade800
      ..strokeWidth = 8;
    canvas.drawLine(
      Offset(size.x * 0.75, size.y * 0.5),
      Offset(size.x * 1.2, size.y * 0.45),
      cannonPaint,
    );

    // الجنازير
    final trackPaint = Paint()..color = Colors.black;
    for (int i = 0; i < 6; i++) {
      canvas.drawCircle(
        Offset(size.x * 0.1 + i * 15, size.y * 0.85),
        6,
        trackPaint,
      );
    }
  }

  // رسم شاحنة
  void _drawTruck(Canvas canvas) {
    // جسم الشاحنة
    final bodyPaint = Paint()..color = Colors.blue.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, size.y * 0.3, size.x * 0.7, size.y * 0.5),
        const Radius.circular(6),
      ),
      bodyPaint,
    );

    // المقطورة
    final trailerPaint = Paint()..color = Colors.blue.shade400;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.7, size.y * 0.2, size.x * 0.3, size.y * 0.6),
        const Radius.circular(4),
      ),
      trailerPaint,
    );

    // العجلات
    final wheelPaint = Paint()..color = Colors.black;
    canvas.drawCircle(Offset(size.x * 0.2, size.y * 0.85), 8, wheelPaint);
    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.85), 8, wheelPaint);
    canvas.drawCircle(Offset(size.x * 0.8, size.y * 0.85), 8, wheelPaint);

    // النوافذ
    final windowPaint = Paint()..color = Colors.lightBlue.shade200;
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.05, size.y * 0.35, size.x * 0.15, size.y * 0.2),
      windowPaint,
    );
  }

  // رسم قطار
  void _drawTrain(Canvas canvas) {
    // جسم القطار
    final bodyPaint = Paint()..color = Colors.grey.shade700;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, size.y * 0.3, size.x, size.y * 0.5),
        const Radius.circular(8),
      ),
      bodyPaint,
    );

    // النوافذ
    final windowPaint = Paint()..color = Colors.lightBlue.shade200;
    for (int i = 0; i < 4; i++) {
      canvas.drawRect(
        Rect.fromLTWH(size.x * 0.1 + i * 30, size.y * 0.35, 20, size.y * 0.2),
        windowPaint,
      );
    }

    // العجلات
    final wheelPaint = Paint()..color = Colors.black;
    for (int i = 0; i < 6; i++) {
      canvas.drawCircle(
        Offset(size.x * 0.1 + i * 25, size.y * 0.85),
        8,
        wheelPaint,
      );
    }

    // المدخنة
    final chimneyPaint = Paint()..color = Colors.grey.shade800;
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.8, size.y * 0.1, 15, size.y * 0.2),
      chimneyPaint,
    );

    // الدخان
    final smokePaint = Paint()..color = Colors.grey.shade400;
    for (int i = 0; i < 3; i++) {
      canvas.drawCircle(
        Offset(size.x * 0.87 + sin(_animationFrame * 2 + i) * 10, size.y * 0.05 - i * 15),
        5 + i * 2,
        smokePaint,
      );
    }
  }

  // رسم مبنى
  void _drawBuilding(Canvas canvas) {
    // جسم المبنى
    final bodyPaint = Paint()..color = Colors.brown.shade600;
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.x, size.y),
      bodyPaint,
    );

    // النوافذ
    final windowPaint = Paint()..color = Colors.yellow.shade300;
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 5; j++) {
        if (Random().nextBool()) {
          canvas.drawRect(
            Rect.fromLTWH(
              size.x * 0.1 + i * 20,
              size.y * 0.1 + j * 20,
              12,
              15,
            ),
            windowPaint,
          );
        }
      }
    }

    // السطح
    final roofPaint = Paint()..color = Colors.red.shade600;
    final roofPath = Path();
    roofPath.moveTo(0, 0);
    roofPath.lineTo(size.x * 0.5, -20);
    roofPath.lineTo(size.x, 0);
    roofPath.close();
    canvas.drawPath(roofPath, roofPaint);
  }

  // رسم سياج
  void _drawFence(Canvas canvas) {
    final fencePaint = Paint()..color = Colors.brown.shade400;

    // الأعمدة
    for (int i = 0; i < 5; i++) {
      canvas.drawRect(
        Rect.fromLTWH(i * 15, size.y * 0.2, 8, size.y * 0.8),
        fencePaint,
      );
    }

    // الألواح الأفقية
    for (int i = 0; i < 3; i++) {
      canvas.drawRect(
        Rect.fromLTWH(0, size.y * 0.3 + i * 20, size.x, 6),
        fencePaint,
      );
    }
  }

  // رسم أشواك
  void _drawSpikes(Canvas canvas) {
    final spikePaint = Paint()..color = Colors.grey.shade800;

    for (int i = 0; i < 8; i++) {
      final spikePath = Path();
      final baseX = (i * 10).toDouble();
      spikePath.moveTo(baseX, size.y);
      spikePath.lineTo(baseX + 5, 0);
      spikePath.lineTo(baseX + 10, size.y);
      spikePath.close();
      canvas.drawPath(spikePath, spikePaint);
    }

    // تأثير معدني
    final metalPaint = Paint()..color = Colors.grey.shade600;
    canvas.drawRect(
      Rect.fromLTWH(0, size.y * 0.8, size.x, size.y * 0.2),
      metalPaint,
    );
  }

  // رسم شعاع ليزر
  void _drawLaser(Canvas canvas) {
    // القاعدة
    final basePaint = Paint()..color = Colors.grey.shade700;
    canvas.drawRect(
      Rect.fromLTWH(0, size.y * 0.8, size.x, size.y * 0.2),
      basePaint,
    );

    // الشعاع المتحرك
    final laserPaint = Paint()
      ..color = Colors.red.shade600
      ..strokeWidth = 8;

    final intensity = sin(_animationFrame * 10) * 0.5 + 0.5;
    final laserColor = Color.lerp(Colors.red.shade300, Colors.red.shade800, intensity)!;

    canvas.drawLine(
      Offset(size.x * 0.5, size.y * 0.8),
      Offset(size.x * 0.5, 0),
      Paint()..color = laserColor..strokeWidth = 6,
    );

    // تأثير الوهج
    final glowPaint = Paint()
      ..color = Colors.red.withAlpha(100)
      ..strokeWidth = 12;
    canvas.drawLine(
      Offset(size.x * 0.5, size.y * 0.8),
      Offset(size.x * 0.5, 0),
      glowPaint,
    );
  }

  // رسم روبوت
  void _drawRobot(Canvas canvas) {
    // الجسم
    final bodyPaint = Paint()..color = Colors.grey.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.2, size.y * 0.4, size.x * 0.6, size.y * 0.4),
        const Radius.circular(8),
      ),
      bodyPaint,
    );

    // الرأس
    final headPaint = Paint()..color = Colors.grey.shade500;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.3, size.y * 0.1, size.x * 0.4, size.y * 0.3),
        const Radius.circular(6),
      ),
      headPaint,
    );

    // العيون المتوهجة
    final eyePaint = Paint()..color = Colors.red;
    if ((_animationFrame * 5).toInt() % 2 == 0) {
      canvas.drawCircle(Offset(size.x * 0.4, size.y * 0.2), 3, eyePaint);
      canvas.drawCircle(Offset(size.x * 0.6, size.y * 0.2), 3, eyePaint);
    }

    // الأذرع
    final armPaint = Paint()..color = Colors.grey.shade700;
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.1, size.y * 0.5, size.x * 0.1, size.y * 0.2),
      armPaint,
    );
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.8, size.y * 0.5, size.x * 0.1, size.y * 0.2),
      armPaint,
    );

    // الأرجل
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.3, size.y * 0.8, size.x * 0.1, size.y * 0.2),
      armPaint,
    );
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.6, size.y * 0.8, size.x * 0.1, size.y * 0.2),
      armPaint,
    );

    // هوائي
    final antennaPaint = Paint()
      ..color = Colors.yellow
      ..strokeWidth = 2;
    canvas.drawLine(
      Offset(size.x * 0.5, size.y * 0.1),
      Offset(size.x * 0.5, size.y * 0.05),
      antennaPaint,
    );
    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.05), 2, Paint()..color = Colors.yellow);
  }
}

// عملة خاصة بقيمة أعلى
class SpecialCoin extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double coinSize = 40.0;
  late Timer _animationTimer;
  double _animationFrame = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    size = Vector2.all(coinSize);
    add(RectangleHitbox());

    _animationTimer = Timer(
      0.1,
      repeat: true,
      onTick: () {
        _animationFrame += 0.2;
      },
    );
    _animationTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);
    _animationTimer.update(dt);

    position.x -= gameRef.gameSpeed * dt;

    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    // عملة ذهبية متوهجة
    final goldPaint = Paint()
      ..shader = RadialGradient(
        colors: [Colors.yellow.shade300, Colors.orange.shade600, Colors.yellow.shade800],
      ).createShader(Rect.fromCircle(center: Offset(size.x * 0.5, size.y * 0.5), radius: size.x * 0.4));

    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.5), size.x * 0.4, goldPaint);

    // تأثير الوهج
    final glowPaint = Paint()..color = Colors.yellow.withAlpha(100);
    for (int i = 0; i < 3; i++) {
      canvas.drawCircle(
        Offset(size.x * 0.5, size.y * 0.5),
        size.x * 0.4 + sin(_animationFrame + i) * 8,
        glowPaint,
      );
    }

    // رمز النجمة
    final starPaint = Paint()..color = Colors.white;
    _drawStar(canvas, Offset(size.x * 0.5, size.y * 0.5), 8, starPaint);
  }

  void _drawStar(Canvas canvas, Offset center, double radius, Paint paint) {
    final path = Path();
    for (int i = 0; i < 5; i++) {
      final angle = (i * 2 * pi / 5) - pi / 2;
      final x = center.dx + cos(angle) * radius;
      final y = center.dy + sin(angle) * radius;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      final innerAngle = ((i + 0.5) * 2 * pi / 5) - pi / 2;
      final innerX = center.dx + cos(innerAngle) * radius * 0.4;
      final innerY = center.dy + sin(innerAngle) * radius * 0.4;
      path.lineTo(innerX, innerY);
    }
    path.close();
    canvas.drawPath(path, paint);
  }
}

// عنصر مكافأة خاص
class BonusItem extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double bonusSize = 50.0;
  late Timer _animationTimer;
  late BonusType _type;
  double _animationFrame = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    size = Vector2.all(bonusSize);
    add(RectangleHitbox());

    // اختيار نوع مكافأة عشوائي
    _type = BonusType.values[Random().nextInt(BonusType.values.length)];

    _animationTimer = Timer(
      0.1,
      repeat: true,
      onTick: () {
        _animationFrame += 0.15;
      },
    );
    _animationTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);
    _animationTimer.update(dt);

    position.x -= gameRef.gameSpeed * dt;

    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    switch (_type) {
      case BonusType.shield:
        _drawShield(canvas);
        break;
      case BonusType.magnet:
        _drawMagnet(canvas);
        break;
      case BonusType.speedBoost:
        _drawSpeedBoost(canvas);
        break;
      case BonusType.doublePoints:
        _drawDoublePoints(canvas);
        break;
    }
  }

  void _drawShield(Canvas canvas) {
    final shieldPaint = Paint()..color = Colors.blue.shade600;
    final path = Path();
    path.moveTo(size.x * 0.5, size.y * 0.1);
    path.lineTo(size.x * 0.8, size.y * 0.3);
    path.lineTo(size.x * 0.8, size.y * 0.7);
    path.lineTo(size.x * 0.5, size.y * 0.9);
    path.lineTo(size.x * 0.2, size.y * 0.7);
    path.lineTo(size.x * 0.2, size.y * 0.3);
    path.close();
    canvas.drawPath(path, shieldPaint);

    // تأثير الحماية
    final glowPaint = Paint()
      ..color = Colors.blue.withAlpha(100)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    canvas.drawPath(path, glowPaint);
  }

  void _drawMagnet(Canvas canvas) {
    final magnetPaint = Paint()..color = Colors.red.shade600;

    // شكل المغناطيس
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.2, size.y * 0.2, size.x * 0.2, size.y * 0.6),
        const Radius.circular(4),
      ),
      magnetPaint,
    );

    final bluePaint = Paint()..color = Colors.blue.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.6, size.y * 0.2, size.x * 0.2, size.y * 0.6),
        const Radius.circular(4),
      ),
      bluePaint,
    );

    // خطوط المجال المغناطيسي
    final fieldPaint = Paint()
      ..color = Colors.yellow.withAlpha(150)
      ..strokeWidth = 2;

    for (int i = 0; i < 5; i++) {
      final radius = 15 + i * 5 + sin(_animationFrame * 3) * 3;
      canvas.drawCircle(
        Offset(size.x * 0.5, size.y * 0.5),
        radius,
        Paint()..color = Colors.yellow.withAlpha(50)..style = PaintingStyle.stroke..strokeWidth = 1,
      );
    }
  }

  void _drawSpeedBoost(Canvas canvas) {
    final speedPaint = Paint()..color = Colors.orange.shade600;

    // شكل البرق
    final path = Path();
    path.moveTo(size.x * 0.4, size.y * 0.1);
    path.lineTo(size.x * 0.7, size.y * 0.4);
    path.lineTo(size.x * 0.5, size.y * 0.4);
    path.lineTo(size.x * 0.6, size.y * 0.9);
    path.lineTo(size.x * 0.3, size.y * 0.6);
    path.lineTo(size.x * 0.5, size.y * 0.6);
    path.close();

    canvas.drawPath(path, speedPaint);

    // تأثير السرعة
    final trailPaint = Paint()..color = Colors.orange.withAlpha(100);
    for (int i = 0; i < 3; i++) {
      canvas.drawPath(
        path,
        Paint()..color = Colors.orange.withAlpha(100 - i * 30),
      );
    }
  }

  void _drawDoublePoints(Canvas canvas) {
    final pointsPaint = Paint()..color = Colors.purple.shade600;

    // رقم 2
    final textPainter = TextPainter(
      text: TextSpan(
        text: '2X',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(size.x * 0.5 - textPainter.width * 0.5, size.y * 0.5 - textPainter.height * 0.5),
    );

    // دائرة خلفية
    canvas.drawCircle(
      Offset(size.x * 0.5, size.y * 0.5),
      size.x * 0.4,
      pointsPaint,
    );

    // تأثير النقاط
    final sparkPaint = Paint()..color = Colors.yellow;
    for (int i = 0; i < 8; i++) {
      final angle = i * pi / 4 + _animationFrame;
      final sparkX = size.x * 0.5 + cos(angle) * 20;
      final sparkY = size.y * 0.5 + sin(angle) * 20;
      canvas.drawCircle(Offset(sparkX, sparkY), 2, sparkPaint);
    }
  }
}

// أنواع المكافآت
enum BonusType {
  shield,        // درع حماية
  magnet,        // مغناطيس لجذب العملات
  speedBoost,    // زيادة السرعة
  doublePoints,  // مضاعفة النقاط
}

// أنواع العوائق المتحركة
enum MovingObstacleType {
  fromTop,       // من الأعلى
  fromBottom,    // من الأسفل
  fromLeft,      // من اليسار
  fromRight,     // من اليمين
  diagonal,      // قطري
}

// أنواع المستويات المختلفة
enum LevelType {
  city,          // مدينة
  desert,        // صحراء
  forest,        // غابة
  mountain,      // جبال
  ocean,         // محيط
  space,         // فضاء
  volcano,       // بركان
  ice,           // جليد
  jungle,        // أدغال
  cave,          // كهف
  sky,           // سماء
  underground,   // تحت الأرض
  factory,       // مصنع
  beach,         // شاطئ
  swamp,         // مستنقع
  ruins,         // أطلال
  cyberpunk,     // سايبر بانك
  medieval,      // عصور وسطى
  futuristic,    // مستقبلي
  alien,         // كوكب غريب
}



// Professional Coin Component
class ProfessionalCoin extends CircleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double coinRadius = 18.0;

  late Timer _sparkleTimer;
  bool _isSparkleVisible = false;
  double _rotationAngle = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    radius = coinRadius;
    paint.color = Colors.amber;

    add(CircleHitbox());

    // Sparkle animation
    _sparkleTimer = Timer(
      0.4,
      repeat: true,
      onTick: () {
        _isSparkleVisible = !_isSparkleVisible;
      },
    );
    _sparkleTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    _sparkleTimer.update(dt);

    // Move coin to the left
    position.x -= gameRef.gameSpeed * dt;

    // Rotate coin
    _rotationAngle += 6 * dt;

    // Bobbing effect
    position.y += sin(DateTime.now().millisecondsSinceEpoch / 200) * 0.5;

    // Remove when off screen
    if (position.x + radius < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    canvas.save();
    canvas.rotate(_rotationAngle);

    // رسم عملة ذهبية احترافية
    _drawProfessionalCoin(canvas);

    // Sparkles
    if (_isSparkleVisible) {
      _drawSparkles(canvas);
    }

    canvas.restore();
  }

  void _drawProfessionalCoin(Canvas canvas) {
    // الظل
    final shadowPaint = Paint()
      ..color = Colors.black.withAlpha(100)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    canvas.drawCircle(const Offset(2, 2), radius, shadowPaint);

    // الجسم الرئيسي للعملة (تدرج ذهبي)
    final coinGradient = RadialGradient(
      colors: [
        const Color(0xFFFFD700), // ذهبي فاتح
        const Color(0xFFB8860B), // ذهبي داكن
        const Color(0xFF8B7355), // بني ذهبي
      ],
      stops: const [0.0, 0.7, 1.0],
    );

    final coinPaint = Paint()
      ..shader = coinGradient.createShader(
        Rect.fromCircle(center: Offset.zero, radius: radius),
      );
    canvas.drawCircle(Offset.zero, radius, coinPaint);

    // الحافة الخارجية
    final outerBorderPaint = Paint()
      ..color = const Color(0xFF8B7355)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawCircle(Offset.zero, radius, outerBorderPaint);

    // الحافة الداخلية
    final innerBorderPaint = Paint()
      ..color = const Color(0xFFFFD700)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    canvas.drawCircle(Offset.zero, radius * 0.8, innerBorderPaint);

    // رمز الدولار المحسن
    _drawDollarSign(canvas);

    // نقاط زخرفية حول الحافة
    _drawDecorativePoints(canvas);
  }

  void _drawDollarSign(Canvas canvas) {
    final dollarPaint = Paint()
      ..color = const Color(0xFF8B4513) // بني داكن
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // الخط العمودي
    canvas.drawLine(
      const Offset(0, -10),
      const Offset(0, 10),
      dollarPaint,
    );

    // الجزء العلوي من S
    final upperPath = Path();
    upperPath.moveTo(-6, -6);
    upperPath.quadraticBezierTo(0, -10, 6, -6);
    upperPath.quadraticBezierTo(0, -2, -6, 2);
    canvas.drawPath(upperPath, dollarPaint);

    // الجزء السفلي من S
    final lowerPath = Path();
    lowerPath.moveTo(6, -2);
    lowerPath.quadraticBezierTo(0, 2, -6, 2);
    lowerPath.quadraticBezierTo(0, 6, 6, 6);
    canvas.drawPath(lowerPath, dollarPaint);
  }

  void _drawDecorativePoints(Canvas canvas) {
    final pointPaint = Paint()
      ..color = const Color(0xFFFFD700)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 8; i++) {
      final angle = (i * 2 * pi) / 8;
      final x = (radius * 0.9) * cos(angle);
      final y = (radius * 0.9) * sin(angle);
      canvas.drawCircle(Offset(x, y), 1.5, pointPaint);
    }
  }

  void _drawStar(Canvas canvas, Paint paint) {
    final path = Path();
    final outerRadius = radius * 0.4;
    final innerRadius = radius * 0.2;

    for (int i = 0; i < 10; i++) {
      final angle = (i * pi) / 5;
      final r = i.isEven ? outerRadius : innerRadius;
      final x = r * cos(angle);
      final y = r * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    canvas.drawPath(path, paint);
  }

  void _drawSparkles(Canvas canvas) {
    final sparklePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2;

    final sparklePositions = [
      Offset(-radius * 1.3, -radius * 0.9),
      Offset(radius * 1.3, -radius * 0.9),
      Offset(-radius * 1.3, radius * 0.9),
      Offset(radius * 1.3, radius * 0.9),
    ];

    for (final pos in sparklePositions) {
      canvas.drawLine(
        Offset(pos.dx - 4, pos.dy),
        Offset(pos.dx + 4, pos.dy),
        sparklePaint,
      );
      canvas.drawLine(
        Offset(pos.dx, pos.dy - 4),
        Offset(pos.dx, pos.dy + 4),
        sparklePaint,
      );
    }
  }
}

// Professional PowerUp Component
class ProfessionalPowerUp extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double powerUpSize = 35.0;

  late Timer _pulseTimer;
  double _pulseScale = 1.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    size = Vector2(powerUpSize, powerUpSize);
    paint.color = Colors.purple;

    add(RectangleHitbox());

    // Pulse animation
    _pulseTimer = Timer(
      0.5,
      repeat: true,
      onTick: () {
        _pulseScale = _pulseScale == 1.0 ? 1.2 : 1.0;
      },
    );
    _pulseTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    _pulseTimer.update(dt);

    // Move power-up to the left
    position.x -= gameRef.gameSpeed * dt;

    // Floating effect
    position.y += sin(DateTime.now().millisecondsSinceEpoch / 300) * 1;

    // Remove when off screen
    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    canvas.save();
    canvas.scale(_pulseScale);

    super.render(canvas);

    // Power-up symbol (lightning bolt)
    final symbolPaint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.x * 0.3, 0);
    path.lineTo(size.x * 0.7, size.y * 0.4);
    path.lineTo(size.x * 0.5, size.y * 0.4);
    path.lineTo(size.x * 0.7, size.y);
    path.lineTo(size.x * 0.3, size.y * 0.6);
    path.lineTo(size.x * 0.5, size.y * 0.6);
    path.close();

    canvas.drawPath(path, symbolPaint);

    canvas.restore();
  }
}

// العوائق الجوية (طيور صاعقة وصواريخ طائرة)
class AirObstacle extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double airObstacleWidth = 60.0;
  static const double airObstacleHeight = 50.0;

  late AirObstacleType _type;
  late Timer _attackTimer;
  late Timer _animationTimer;
  bool _isAttacking = false;
  double _animationFrame = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // اختيار نوع عائق جوي عشوائي
    final random = Random();
    _type = AirObstacleType.values[random.nextInt(AirObstacleType.values.length)];

    _setupAirObstacleByType();

    add(RectangleHitbox());

    // Timer للهجوم (إطلاق الصواريخ أو الأحجار)
    _attackTimer = Timer(
      2.0 + random.nextDouble() * 2.0, // هجوم كل 2-4 ثواني
      repeat: true,
      onTick: _performAttack,
    );
    _attackTimer.start();

    // Timer للحركة والرسوم المتحركة
    _animationTimer = Timer(
      0.1,
      repeat: true,
      onTick: () {
        _animationFrame += 0.1;
      },
    );
    _animationTimer.start();
  }

  void _setupAirObstacleByType() {
    switch (_type) {
      case AirObstacleType.thunderBird:
        size = Vector2(airObstacleWidth, airObstacleHeight);
        paint.color = Colors.purple.shade600;
        break;
      case AirObstacleType.fireBird:
        size = Vector2(airObstacleWidth, airObstacleHeight);
        paint.color = Colors.red.shade600;
        break;
      case AirObstacleType.flyingMissile:
        size = Vector2(50, 30);
        paint.color = Colors.orange.shade600;
        break;
    }
  }

  @override
  void update(double dt) {
    super.update(dt);

    _attackTimer.update(dt);
    _animationTimer.update(dt);

    // حركة العائق الجوي
    position.x -= gameRef.gameSpeed * dt;

    // حركة تموجية في الهواء
    position.y += sin(_animationFrame * 3) * 0.5;

    // إزالة العائق عند الخروج من الشاشة
    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  void _performAttack() {
    if (gameRef._isGameOver) return;

    _isAttacking = true;

    // إنشاء مقذوف حسب نوع العائق مع الأصوات
    switch (_type) {
      case AirObstacleType.thunderBird:
        SoundManager.playSound('thunder');
        _createThunderBolt();
        break;
      case AirObstacleType.fireBird:
        SoundManager.playSound('fire');
        _createFireBall();
        break;
      case AirObstacleType.flyingMissile:
        SoundManager.playSound('missile');
        _createMissileAttack();
        break;
    }

    // إيقاف الهجوم بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 500), () {
      _isAttacking = false;
    });
  }

  void _createThunderBolt() {
    final thunderBolt = AirProjectile(
      type: ProjectileType.thunder,
      startPosition: Vector2(position.x, position.y + size.y),
      targetY: gameRef.size.y - 150, // نحو الأرض
    );
    gameRef.add(thunderBolt);
  }

  void _createFireBall() {
    final fireBall = AirProjectile(
      type: ProjectileType.fire,
      startPosition: Vector2(position.x, position.y + size.y),
      targetY: gameRef.size.y - 150,
    );
    gameRef.add(fireBall);
  }

  void _createMissileAttack() {
    final missile = AirProjectile(
      type: ProjectileType.missile,
      startPosition: Vector2(position.x, position.y + size.y),
      targetY: gameRef.size.y - 150,
    );
    gameRef.add(missile);
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    switch (_type) {
      case AirObstacleType.thunderBird:
        _drawThunderBird(canvas);
        break;
      case AirObstacleType.fireBird:
        _drawFireBird(canvas);
        break;
      case AirObstacleType.flyingMissile:
        _drawFlyingMissile(canvas);
        break;
    }
  }

  void _drawThunderBird(Canvas canvas) {
    // جسم الطائر
    final bodyPaint = Paint()..color = Colors.purple.shade600;
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.2, size.y * 0.3, size.x * 0.6, size.y * 0.4),
      bodyPaint,
    );

    // الأجنحة (متحركة)
    final wingPaint = Paint()..color = Colors.purple.shade400;
    final wingOffset = sin(_animationFrame * 10) * 5;

    // الجناح الأيسر
    canvas.drawOval(
      Rect.fromLTWH(0, size.y * 0.2 + wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // الجناح الأيمن
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.7, size.y * 0.2 - wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // العيون المتوهجة
    final eyePaint = Paint()..color = Colors.yellow;
    canvas.drawCircle(Offset(size.x * 0.4, size.y * 0.4), 3, eyePaint);
    canvas.drawCircle(Offset(size.x * 0.6, size.y * 0.4), 3, eyePaint);

    // البرق حول الطائر
    if (_isAttacking) {
      final lightningPaint = Paint()
        ..color = Colors.yellow
        ..strokeWidth = 2;

      for (int i = 0; i < 3; i++) {
        final startX = size.x * 0.5 + sin(_animationFrame * 5 + i) * 20;
        final startY = size.y * 0.5 + cos(_animationFrame * 5 + i) * 15;
        canvas.drawLine(
          Offset(startX, startY),
          Offset(startX + 10, startY + 15),
          lightningPaint,
        );
      }
    }
  }

  void _drawFireBird(Canvas canvas) {
    // جسم الطائر الناري
    final bodyPaint = Paint()
      ..shader = RadialGradient(
        colors: [Colors.red, Colors.orange, Colors.yellow],
      ).createShader(Rect.fromLTWH(0, 0, size.x, size.y));

    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.2, size.y * 0.3, size.x * 0.6, size.y * 0.4),
      bodyPaint,
    );

    // الأجنحة النارية
    final wingPaint = Paint()..color = Colors.orange.shade600;
    final wingOffset = sin(_animationFrame * 8) * 3;

    canvas.drawOval(
      Rect.fromLTWH(0, size.y * 0.2 + wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.7, size.y * 0.2 - wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // اللهب حول الطائر
    final flamePaint = Paint()..color = Colors.red.withAlpha(150);
    for (int i = 0; i < 5; i++) {
      final flameX = size.x * 0.5 + sin(_animationFrame * 4 + i) * 15;
      final flameY = size.y * 0.5 + cos(_animationFrame * 4 + i) * 10;
      canvas.drawCircle(Offset(flameX, flameY), 3, flamePaint);
    }
  }

  void _drawFlyingMissile(Canvas canvas) {
    // جسم الصاروخ الطائر
    final missilePaint = Paint()..color = Colors.orange.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, size.y * 0.3, size.x * 0.8, size.y * 0.4),
        const Radius.circular(5),
      ),
      missilePaint,
    );

    // رأس الصاروخ
    final nosePaint = Paint()..color = Colors.red.shade600;
    final nosePath = Path();
    nosePath.moveTo(size.x * 0.8, size.y * 0.5);
    nosePath.lineTo(size.x, size.y * 0.3);
    nosePath.lineTo(size.x, size.y * 0.7);
    nosePath.close();
    canvas.drawPath(nosePath, nosePaint);

    // اللهب الخلفي
    final flamePaint = Paint()..color = Colors.blue.shade400;
    canvas.drawOval(
      Rect.fromLTWH(-10, size.y * 0.4, 15, size.y * 0.2),
      flamePaint,
    );
  }
}

// أنواع العوائق الجوية
enum AirObstacleType {
  thunderBird,   // طائر البرق
  fireBird,      // طائر النار
  flyingMissile, // صاروخ طائر
}

// أنواع المقذوفات
enum ProjectileType {
  thunder,  // برق
  fire,     // نار
  missile,  // صاروخ
}

// المقذوفات التي تطلقها العوائق الجوية
class AirProjectile extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  final ProjectileType type;
  final Vector2 startPosition;
  final double targetY;

  late double _speed;
  late Timer _effectTimer;
  double _effectFrame = 0.0;

  AirProjectile({
    required this.type,
    required this.startPosition,
    required this.targetY,
  });

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    position = startPosition.clone();

    switch (type) {
      case ProjectileType.thunder:
        size = Vector2(8, 30);
        _speed = 400;
        paint.color = Colors.yellow;
        break;
      case ProjectileType.fire:
        size = Vector2(12, 12);
        _speed = 300;
        paint.color = Colors.red;
        break;
      case ProjectileType.missile:
        size = Vector2(6, 20);
        _speed = 500;
        paint.color = Colors.orange;
        break;
    }

    add(RectangleHitbox());

    _effectTimer = Timer(
      0.05,
      repeat: true,
      onTick: () {
        _effectFrame += 0.05;
      },
    );
    _effectTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    _effectTimer.update(dt);

    // حركة المقذوف نحو الأسفل
    position.y += _speed * dt;

    // إزالة المقذوف عند وصوله للأرض أو خروجه من الشاشة
    if (position.y > gameRef.size.y || position.x < -50) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    switch (type) {
      case ProjectileType.thunder:
        _drawThunderBolt(canvas);
        break;
      case ProjectileType.fire:
        _drawFireBall(canvas);
        break;
      case ProjectileType.missile:
        _drawMissileProjectile(canvas);
        break;
    }
  }

  void _drawThunderBolt(Canvas canvas) {
    final lightningPaint = Paint()
      ..color = Colors.yellow
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    // رسم البرق المتعرج
    final path = Path();
    path.moveTo(size.x * 0.5, 0);
    path.lineTo(size.x * 0.3, size.y * 0.3);
    path.lineTo(size.x * 0.7, size.y * 0.6);
    path.lineTo(size.x * 0.5, size.y);

    canvas.drawPath(path, lightningPaint);

    // توهج حول البرق
    final glowPaint = Paint()
      ..color = Colors.yellow.withAlpha(100)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);
    canvas.drawPath(path, glowPaint);
  }

  void _drawFireBall(Canvas canvas) {
    // كرة النار الرئيسية
    final firePaint = Paint()
      ..shader = RadialGradient(
        colors: [Colors.yellow, Colors.orange, Colors.red],
      ).createShader(Rect.fromCircle(center: Offset(size.x * 0.5, size.y * 0.5), radius: size.x * 0.5));

    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.5), size.x * 0.5, firePaint);

    // شرارات حول كرة النار
    final sparkPaint = Paint()..color = Colors.orange;
    for (int i = 0; i < 4; i++) {
      final sparkX = size.x * 0.5 + sin(_effectFrame * 8 + i) * 8;
      final sparkY = size.y * 0.5 + cos(_effectFrame * 8 + i) * 8;
      canvas.drawCircle(Offset(sparkX, sparkY), 1, sparkPaint);
    }
  }

  void _drawMissileProjectile(Canvas canvas) {
    // جسم الصاروخ الصغير
    final missilePaint = Paint()..color = Colors.orange;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.2, 0, size.x * 0.6, size.y * 0.8),
        const Radius.circular(2),
      ),
      missilePaint,
    );

    // رأس الصاروخ
    final nosePaint = Paint()..color = Colors.red;
    final nosePath = Path();
    nosePath.moveTo(size.x * 0.5, 0);
    nosePath.lineTo(size.x * 0.2, size.y * 0.2);
    nosePath.lineTo(size.x * 0.8, size.y * 0.2);
    nosePath.close();
    canvas.drawPath(nosePath, nosePaint);

    // اللهب الخلفي
    final flamePaint = Paint()..color = Colors.blue;
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.3, size.y * 0.8, size.x * 0.4, size.y * 0.2),
      flamePaint,
    );
  }
}