import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations to portrait only
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const ArzaRushApp());
}

class ArzaRushApp extends StatelessWidget {
  const ArzaRushApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ArzaRush',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.orange,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const SubwaySurfers3DGame(),
    );
  }
}

// لعبة ArzaRush ثلاثية الأبعاد - محاكاة Subway Surfers
class SubwaySurfers3DGame extends StatefulWidget {
  const SubwaySurfers3DGame({super.key});

  @override
  State<SubwaySurfers3DGame> createState() => _SubwaySurfers3DGameState();
}

class _SubwaySurfers3DGameState extends State<SubwaySurfers3DGame>
    with TickerProviderStateMixin {

  // Game state
  int score = 0;
  bool isGameOver = false;
  bool isJumping = false;
  bool isSliding = false;

  // Player position (3 lanes: -1, 0, 1)
  int currentLane = 0;
  double playerY = 0; // For jumping

  // Game speed and distance
  double gameSpeed = 1.0;
  double distance = 0;

  // Animation controllers
  late AnimationController _runAnimationController;
  late AnimationController _jumpAnimationController;
  late AnimationController _slideAnimationController;
  late AnimationController _backgroundController;

  // Obstacles and coins
  List<GameObstacle> obstacles = [];
  List<GameCoin> coins = [];

  // Timers
  Timer? _gameTimer;
  Timer? _obstacleTimer;
  Timer? _coinTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startGame();
  }

  void _initializeAnimations() {
    _runAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    )..repeat();

    _jumpAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _backgroundController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  void _startGame() {
    // Game loop
    _gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      setState(() {
        _updateGame();
      });
    });

    // Spawn obstacles
    _obstacleTimer = Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      _spawnObstacle();
    });

    // Spawn coins
    _coinTimer = Timer.periodic(const Duration(milliseconds: 800), (timer) {
      _spawnCoin();
    });
  }

  void _updateGame() {
    if (isGameOver) return;

    // Update distance and score
    distance += gameSpeed;
    score = (distance / 10).round();

    // Increase game speed gradually
    gameSpeed += 0.001;

    // Update obstacles
    obstacles.removeWhere((obstacle) {
      obstacle.z += gameSpeed * 20;
      return obstacle.z > 100;
    });

    // Update coins
    coins.removeWhere((coin) {
      coin.z += gameSpeed * 20;
      return coin.z > 100;
    });

    // Check collisions
    _checkCollisions();
  }

  void _spawnObstacle() {
    if (isGameOver) return;

    final random = Random();
    final lane = random.nextInt(3) - 1; // -1, 0, or 1

    obstacles.add(GameObstacle(
      lane: lane,
      z: -50,
      type: ObstacleType.values[random.nextInt(ObstacleType.values.length)],
    ));
  }

  void _spawnCoin() {
    if (isGameOver) return;

    final random = Random();
    final lane = random.nextInt(3) - 1;

    coins.add(GameCoin(
      lane: lane,
      z: -50,
      y: random.nextDouble() * 30 + 10,
    ));
  }

  void _checkCollisions() {
    // Check obstacle collisions
    for (final obstacle in obstacles) {
      if (obstacle.lane == currentLane &&
          obstacle.z > -5 && obstacle.z < 5 &&
          !isJumping && !isSliding) {
        _gameOver();
        return;
      }
    }

    // Check coin collection
    coins.removeWhere((coin) {
      if (coin.lane == currentLane &&
          coin.z > -3 && coin.z < 3) {
        score += 10;
        HapticFeedback.lightImpact();
        return true;
      }
      return false;
    });
  }

  void _gameOver() {
    setState(() {
      isGameOver = true;
    });

    _gameTimer?.cancel();
    _obstacleTimer?.cancel();
    _coinTimer?.cancel();

    HapticFeedback.heavyImpact();
  }

  void _resetGame() {
    setState(() {
      isGameOver = false;
      score = 0;
      distance = 0;
      gameSpeed = 1.0;
      currentLane = 0;
      playerY = 0;
      isJumping = false;
      isSliding = false;
      obstacles.clear();
      coins.clear();
    });

    _startGame();
  }

  // Player controls
  void _jump() {
    if (isGameOver || isJumping || isSliding) return;

    setState(() {
      isJumping = true;
    });

    _jumpAnimationController.forward().then((_) {
      setState(() {
        isJumping = false;
      });
      _jumpAnimationController.reset();
    });

    HapticFeedback.lightImpact();
  }

  void _slide() {
    if (isGameOver || isJumping || isSliding) return;

    setState(() {
      isSliding = true;
    });

    _slideAnimationController.forward().then((_) {
      setState(() {
        isSliding = false;
      });
      _slideAnimationController.reset();
    });

    HapticFeedback.lightImpact();
  }

  void _moveLeft() {
    if (isGameOver || currentLane <= -1) return;

    setState(() {
      currentLane--;
    });

    HapticFeedback.selectionClick();
  }

  void _moveRight() {
    if (isGameOver || currentLane >= 1) return;

    setState(() {
      currentLane++;
    });

    HapticFeedback.selectionClick();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: _jump,
        onPanUpdate: (details) {
          // Swipe controls
          if (details.delta.dx > 5) {
            _moveRight();
          } else if (details.delta.dx < -5) {
            _moveLeft();
          } else if (details.delta.dy > 5) {
            _slide();
          }
        },
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF87CEEB), // Sky blue
                Color(0xFF98FB98), // Light green
                Color(0xFF8B4513), // Brown ground
              ],
            ),
          ),
          child: Stack(
            children: [
              // 3D Background with moving elements
              _build3DBackground(),

              // Railway tracks (3D perspective)
              _buildRailwayTracks(),

              // Player character (3D-like)
              _buildPlayer(),

              // Obstacles (3D perspective)
              ..._buildObstacles(),

              // Coins (3D perspective)
              ..._buildCoins(),

              // UI Elements
              _buildUI(),

              // Game Over overlay
              if (isGameOver) _buildGameOverOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  // Build methods for 3D-like UI components
  Widget _build3DBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return CustomPaint(
          size: Size.infinite,
          painter: Background3DPainter(
            animationValue: _backgroundController.value,
            gameSpeed: gameSpeed,
          ),
        );
      },
    );
  }

  Widget _buildRailwayTracks() {
    return CustomPaint(
      size: Size.infinite,
      painter: RailwayTracksPainter(
        gameSpeed: gameSpeed,
        distance: distance,
      ),
    );
  }

  Widget _buildPlayer() {
    final screenWidth = MediaQuery.of(context).size.width;
    final laneWidth = screenWidth / 3;
    final playerX = (currentLane + 1) * laneWidth - laneWidth / 2;

    return AnimatedPositioned(
      duration: const Duration(milliseconds: 200),
      left: playerX - 25,
      bottom: isSliding ? 50 : (isJumping ? 150 + (sin(_jumpAnimationController.value * pi) * 100) : 100),
      child: AnimatedBuilder(
        animation: _runAnimationController,
        builder: (context, child) {
          return Transform.scale(
            scale: isSliding ? 0.7 : 1.0,
            child: Container(
              width: 50,
              height: isSliding ? 35 : 70,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.blue.shade400,
                    Colors.blue.shade800,
                  ],
                ),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Player face
                  Positioned(
                    top: 10,
                    left: 10,
                    right: 10,
                    child: Container(
                      height: 30,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // Eyes
                          Container(
                            width: 6,
                            height: 6,
                            decoration: const BoxDecoration(
                              color: Colors.black,
                              shape: BoxShape.circle,
                            ),
                          ),
                          Container(
                            width: 6,
                            height: 6,
                            decoration: const BoxDecoration(
                              color: Colors.black,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Running animation effect
                  if (!isSliding)
                    Positioned(
                      bottom: 5,
                      left: 5 + (sin(_runAnimationController.value * 2 * pi) * 3),
                      child: Container(
                        width: 8,
                        height: 15,
                        decoration: BoxDecoration(
                          color: Colors.blue.shade600,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  if (!isSliding)
                    Positioned(
                      bottom: 5,
                      right: 5 + (sin(_runAnimationController.value * 2 * pi + pi) * 3),
                      child: Container(
                        width: 8,
                        height: 15,
                        decoration: BoxDecoration(
                          color: Colors.blue.shade600,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildObstacles() {
    return obstacles.map((obstacle) {
      final screenWidth = MediaQuery.of(context).size.width;
      final laneWidth = screenWidth / 3;
      final obstacleX = (obstacle.lane + 1) * laneWidth - laneWidth / 2;

      // 3D perspective calculation
      final scale = max(0.1, 1.0 - (obstacle.z + 50) / 100);
      final opacity = max(0.0, min(1.0, 1.0 - (obstacle.z + 50) / 80));

      return Positioned(
        left: obstacleX - (40 * scale) / 2,
        bottom: 100 + (obstacle.z * 2), // 3D depth effect
        child: Transform.scale(
          scale: scale,
          child: Opacity(
            opacity: opacity,
            child: Container(
              width: 40,
              height: _getObstacleHeight(obstacle.type) * scale,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: _getObstacleColors(obstacle.type),
                ),
                borderRadius: BorderRadius.circular(5),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: _getObstacleIcon(obstacle.type),
            ),
          ),
        ),
      );
    }).toList();
  }

  List<Widget> _buildCoins() {
    return coins.map((coin) {
      final screenWidth = MediaQuery.of(context).size.width;
      final laneWidth = screenWidth / 3;
      final coinX = (coin.lane + 1) * laneWidth - laneWidth / 2;

      // 3D perspective calculation
      final scale = max(0.1, 1.0 - (coin.z + 50) / 100);
      final opacity = max(0.0, min(1.0, 1.0 - (coin.z + 50) / 80));

      return Positioned(
        left: coinX - (20 * scale) / 2,
        bottom: 100 + coin.y + (coin.z * 2),
        child: Transform.scale(
          scale: scale,
          child: Opacity(
            opacity: opacity,
            child: Transform.rotate(
              angle: distance * 0.1, // Rotating coin effect
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  gradient: const RadialGradient(
                    colors: [
                      Color(0xFFFFD700), // Gold
                      Color(0xFFB8860B), // Dark gold
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.yellow.withOpacity(0.5),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildUI() {
    return Positioned(
      top: 50,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Score display
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.orange.shade400,
                  Colors.orange.shade600,
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.star, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  score.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Speed indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.8),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              'Speed: ${gameSpeed.toStringAsFixed(1)}x',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameOverOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'GAME OVER',
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.red,
                shadows: [
                  Shadow(
                    offset: Offset(2, 2),
                    blurRadius: 5,
                    color: Colors.black,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Final Score: $score',
              style: const TextStyle(
                fontSize: 24,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: _resetGame,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: const Text(
                'PLAY AGAIN',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods for obstacles
  double _getObstacleHeight(ObstacleType type) {
    switch (type) {
      case ObstacleType.barrier:
        return 80;
      case ObstacleType.train:
        return 100;
      case ObstacleType.pole:
        return 120;
    }
  }

  List<Color> _getObstacleColors(ObstacleType type) {
    switch (type) {
      case ObstacleType.barrier:
        return [Colors.red.shade400, Colors.red.shade800];
      case ObstacleType.train:
        return [Colors.blue.shade400, Colors.blue.shade800];
      case ObstacleType.pole:
        return [Colors.grey.shade400, Colors.grey.shade800];
    }
  }

  Widget _getObstacleIcon(ObstacleType type) {
    IconData icon;
    switch (type) {
      case ObstacleType.barrier:
        icon = Icons.warning;
        break;
      case ObstacleType.train:
        icon = Icons.train;
        break;
      case ObstacleType.pole:
        icon = Icons.traffic;
        break;
    }

    return Icon(
      icon,
      color: Colors.white,
      size: 20,
    );
  }

  @override
  void dispose() {
    _runAnimationController.dispose();
    _jumpAnimationController.dispose();
    _slideAnimationController.dispose();
    _backgroundController.dispose();
    _gameTimer?.cancel();
    _obstacleTimer?.cancel();
    _coinTimer?.cancel();
    super.dispose();
  }
}

// Game data classes
class GameObstacle {
  final int lane;
  double z;
  final ObstacleType type;

  GameObstacle({
    required this.lane,
    required this.z,
    required this.type,
  });
}

class GameCoin {
  final int lane;
  double z;
  final double y;

  GameCoin({
    required this.lane,
    required this.z,
    required this.y,
  });
}

enum ObstacleType {
  barrier,
  train,
  pole,
}

// Custom painters for 3D effects
class Background3DPainter extends CustomPainter {
  final double animationValue;
  final double gameSpeed;

  Background3DPainter({
    required this.animationValue,
    required this.gameSpeed,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Moving clouds
    final cloudPaint = Paint()..color = Colors.white.withOpacity(0.6);

    for (int i = 0; i < 5; i++) {
      final x = (i * 150 + animationValue * 50) % (size.width + 100);
      final y = 50 + (i * 20);

      canvas.drawCircle(Offset(x, y), 20 + (i * 5), cloudPaint);
      canvas.drawCircle(Offset(x + 15, y), 25 + (i * 3), cloudPaint);
      canvas.drawCircle(Offset(x + 30, y), 20 + (i * 4), cloudPaint);
    }

    // Moving buildings in background
    final buildingPaint = Paint()..color = Colors.grey.withOpacity(0.3);

    for (int i = 0; i < 8; i++) {
      final x = (i * 80 + animationValue * 20) % (size.width + 100);
      final height = 100 + (i * 20);

      canvas.drawRect(
        Rect.fromLTWH(x, size.height - height - 100, 60, height),
        buildingPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class RailwayTracksPainter extends CustomPainter {
  final double gameSpeed;
  final double distance;

  RailwayTracksPainter({
    required this.gameSpeed,
    required this.distance,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final trackPaint = Paint()
      ..color = Colors.brown.shade600
      ..strokeWidth = 8;

    final railPaint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 4;

    // Draw 3 lanes with perspective
    final laneWidth = size.width / 3;

    for (int lane = 0; lane < 3; lane++) {
      final laneCenter = (lane + 0.5) * laneWidth;

      // Draw railway tracks with 3D perspective
      for (int i = 0; i < 20; i++) {
        final y = size.height - 50 - (i * 30) + (distance % 30);
        final scale = max(0.1, 1.0 - (i * 0.05));
        final trackWidth = 40 * scale;

        // Track sleeper
        canvas.drawRect(
          Rect.fromCenter(
            center: Offset(laneCenter, y),
            width: trackWidth,
            height: 6 * scale,
          ),
          trackPaint,
        );

        // Rails
        canvas.drawRect(
          Rect.fromCenter(
            center: Offset(laneCenter - trackWidth * 0.3, y),
            width: 3 * scale,
            height: 30 * scale,
          ),
          railPaint,
        );

        canvas.drawRect(
          Rect.fromCenter(
            center: Offset(laneCenter + trackWidth * 0.3, y),
            width: 3 * scale,
            height: 30 * scale,
          ),
          railPaint,
        );
      }
    }

    // Lane dividers
    final dividerPaint = Paint()
      ..color = Colors.yellow
      ..strokeWidth = 2;

    for (int i = 1; i < 3; i++) {
      final x = i * laneWidth;
      for (int j = 0; j < 10; j++) {
        final y = size.height - 80 - (j * 40) + (distance % 40);
        canvas.drawLine(
          Offset(x, y),
          Offset(x, y - 20),
          dividerPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}


