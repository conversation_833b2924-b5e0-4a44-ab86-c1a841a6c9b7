import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations to portrait only
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const ArzaRushApp());
}

class ArzaRushApp extends StatelessWidget {
  const ArzaRushApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ArzaRush',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.orange,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const SimpleGameScreen(),
    );
  }
}

class SimpleGameScreen extends StatefulWidget {
  const SimpleGameScreen({super.key});

  @override
  State<SimpleGameScreen> createState() => _SimpleGameScreenState();
}

class _SimpleGameScreenState extends State<SimpleGameScreen> {
  late SimpleGame game;

  @override
  void initState() {
    super.initState();
    game = SimpleGame();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () => game.jump(),
        child: GameWidget<SimpleGame>.controlled(
          gameFactory: () => game,
        ),
      ),
    );
  }
}

// Simple game class for testing
class SimpleGame extends FlameGame {
  late RectangleComponent player;
  late RectangleComponent ground;
  double playerY = 0;
  double jumpVelocity = 0;
  bool isJumping = false;
  final double gravity = 800;
  final double jumpPower = -400;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Create ground
    ground = RectangleComponent(
      size: Vector2(size.x, 50),
      position: Vector2(0, size.y - 50),
      paint: Paint()..color = Colors.brown,
    );
    add(ground);

    // Create player
    playerY = size.y - 100;
    player = RectangleComponent(
      size: Vector2(50, 50),
      position: Vector2(100, playerY),
      paint: Paint()..color = Colors.blue,
    );
    add(player);
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Apply gravity
    if (isJumping) {
      jumpVelocity += gravity * dt;
      playerY += jumpVelocity * dt;

      // Check if landed
      if (playerY >= size.y - 100) {
        playerY = size.y - 100;
        jumpVelocity = 0;
        isJumping = false;
      }

      player.position.y = playerY;
    }
  }

  void jump() {
    if (!isJumping) {
      jumpVelocity = jumpPower;
      isJumping = true;
      HapticFeedback.lightImpact();
    }
  }
}


