import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flame_audio/flame_audio.dart';
import 'dart:async';
import 'dart:math';

// مدير الأصوات الاحترافي
class SoundManager {
  static bool _soundEnabled = true;
  static double _volume = 0.7;

  // أصوات اللعبة
  static const String jumpSound = 'jump.mp3';
  static const String flySound = 'fly.mp3';
  static const String landSound = 'land.mp3';
  static const String coinSound = 'coin.mp3';
  static const String powerUpSound = 'powerup.mp3';
  static const String crashSound = 'crash.mp3';
  static const String explosionSound = 'explosion.mp3';
  static const String thunderSound = 'thunder.mp3';
  static const String fireSound = 'fire.mp3';
  static const String missileSound = 'missile.mp3';
  static const String gameOverSound = 'gameover.mp3';
  static const String backgroundMusic = 'background.mp3';
  static const String menuMusic = 'menu.mp3';

  // تشغيل الصوت
  static Future<void> playSound(String soundFile) async {
    if (!_soundEnabled) return;

    try {
      await FlameAudio.play(soundFile, volume: _volume);
    } catch (e) {
      // في حالة عدم وجود الملف الصوتي، نستخدم اهتزاز بديل
      HapticFeedback.lightImpact();
    }
  }

  // تشغيل الموسيقى الخلفية
  static Future<void> playBackgroundMusic(String musicFile) async {
    if (!_soundEnabled) return;

    try {
      await FlameAudio.playLongAudio(musicFile, volume: _volume * 0.5);
    } catch (e) {
      // تجاهل الخطأ إذا لم يكن الملف موجود
    }
  }

  // إيقاف الموسيقى
  static Future<void> stopBackgroundMusic() async {
    try {
      await FlameAudio.audioCache.clearAll();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // تبديل الصوت
  static void toggleSound() {
    _soundEnabled = !_soundEnabled;
  }

  // تعديل مستوى الصوت
  static void setVolume(double volume) {
    _volume = volume.clamp(0.0, 1.0);
  }

  // الحصول على حالة الصوت
  static bool get isSoundEnabled => _soundEnabled;
  static double get volume => _volume;
}

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations to portrait only
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const ArzaRushApp());
}

class ArzaRushApp extends StatelessWidget {
  const ArzaRushApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ArzaRush - Endless Runner',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.orange,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        fontFamily: 'Arial',
      ),
      home: const MainMenuScreen(),
    );
  }
}

// شاشة القائمة الرئيسية
class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen>
    with TickerProviderStateMixin {
  late AnimationController _titleController;
  late AnimationController _buttonController;
  late Animation<double> _titleAnimation;
  late Animation<Offset> _buttonSlideAnimation;

  @override
  void initState() {
    super.initState();

    _titleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _titleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _titleController,
      curve: Curves.elasticOut,
    ));

    _buttonSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.bounceOut,
    ));

    _titleController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _buttonController.forward();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1E3C72),
              Color(0xFF2A5298),
              Color(0xFF3B82F6),
              Color(0xFF8B5CF6),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // عنوان اللعبة مع تأثيرات
                ScaleTransition(
                  scale: _titleAnimation,
                  child: Column(
                    children: [
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [Colors.orange, Colors.red, Colors.purple],
                        ).createShader(bounds),
                        child: const Text(
                          'ARZA',
                          style: TextStyle(
                            fontSize: 80,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [Colors.yellow, Colors.orange, Colors.red],
                        ).createShader(bounds),
                        child: const Text(
                          'RUSH',
                          style: TextStyle(
                            fontSize: 80,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.orange.withOpacity(0.8),
                              Colors.red.withOpacity(0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          'Endless Runner Adventure',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 80),

                // أزرار القائمة
                SlideTransition(
                  position: _buttonSlideAnimation,
                  child: Column(
                    children: [
                      _buildMenuButton(
                        'START GAME',
                        Icons.play_arrow_rounded,
                        [Colors.green.shade400, Colors.green.shade600],
                        () => _startGame(),
                      ),
                      const SizedBox(height: 20),
                      _buildMenuButton(
                        'HIGH SCORES',
                        Icons.emoji_events_rounded,
                        [Colors.yellow.shade400, Colors.orange.shade600],
                        () => _showHighScores(),
                      ),
                      const SizedBox(height: 20),
                      _buildSoundToggleButton(),
                      const SizedBox(height: 20),
                      _buildMenuButton(
                        'SETTINGS',
                        Icons.settings_rounded,
                        [Colors.blue.shade400, Colors.blue.shade600],
                        () => _showSettings(),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 60),

                // معلومات الإصدار
                FadeTransition(
                  opacity: _titleAnimation,
                  child: const Text(
                    'Version 1.0.0 - Made with ❤️',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(
    String text,
    IconData icon,
    List<Color> gradientColors,
    VoidCallback onPressed,
  ) {
    return Container(
      width: 300,
      height: 65,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(35),
        gradient: LinearGradient(colors: gradientColors),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.4),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(35),
          onTap: () {
            HapticFeedback.lightImpact();
            onPressed();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 30,
              ),
              const SizedBox(width: 15),
              Text(
                text,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _startGame() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const ProfessionalGameScreen(),
      ),
    );
  }

  void _showHighScores() {
    // TODO: Implement high scores screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('High Scores coming soon!'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _buildSoundToggleButton() {
    return StatefulBuilder(
      builder: (context, setState) {
        return GestureDetector(
          onTap: () {
            setState(() {
              SoundManager.toggleSound();
            });
            HapticFeedback.lightImpact();
          },
          child: Container(
            width: 280,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: SoundManager.isSoundEnabled
                  ? [Colors.purple.shade400, Colors.purple.shade600]
                  : [Colors.grey.shade400, Colors.grey.shade600],
              ),
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: (SoundManager.isSoundEnabled ? Colors.purple : Colors.grey).withOpacity(0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  SoundManager.isSoundEnabled ? Icons.volume_up_rounded : Icons.volume_off_rounded,
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  SoundManager.isSoundEnabled ? 'SOUND ON' : 'SOUND OFF',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSettings() {
    // TODO: Implement settings screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Settings coming soon!'),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

// شاشة اللعبة الاحترافية
class ProfessionalGameScreen extends StatefulWidget {
  const ProfessionalGameScreen({super.key});

  @override
  State<ProfessionalGameScreen> createState() => _ProfessionalGameScreenState();
}

class _ProfessionalGameScreenState extends State<ProfessionalGameScreen>
    with TickerProviderStateMixin {

  late ProfessionalArzaRushGame game;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    game = ProfessionalArzaRushGame();
  }

  void _pauseGame() {
    setState(() {
      _isPaused = !_isPaused;
      if (_isPaused) {
        game.pauseEngine();
      } else {
        game.resumeEngine();
      }
    });
  }

  void _goToMainMenu() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const MainMenuScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        // آلية التحكم الجديدة - الضغط المستمر للطيران
        onTapDown: (details) {
          if (!_isPaused) {
            game.startFlying(); // بدء الطيران
          }
        },
        onTapUp: (details) {
          if (!_isPaused) {
            game.stopFlying(); // إيقاف الطيران والسقوط
          }
        },
        onTapCancel: () {
          if (!_isPaused) {
            game.stopFlying(); // إيقاف الطيران عند إلغاء اللمس
          }
        },
        child: Stack(
          children: [
            // Game Widget
            GameWidget<ProfessionalArzaRushGame>.controlled(
              gameFactory: () => game,
            ),

            // Professional UI Overlay
            _buildGameUI(),

            // تعليمات التحكم
            _buildControlInstructions(),

            // Pause Overlay
            if (_isPaused) _buildPauseOverlay(),

            // Game Over Overlay
            ValueListenableBuilder<bool>(
              valueListenable: game.gameOverNotifier,
              builder: (context, isGameOver, child) {
                if (!isGameOver) return const SizedBox.shrink();
                return _buildGameOverOverlay();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameUI() {
    return Positioned(
      top: 50,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Score Display with professional design
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.orange.shade400,
                  Colors.orange.shade600,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.star_rounded,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 8),
                ValueListenableBuilder<int>(
                  valueListenable: game.scoreNotifier,
                  builder: (context, score, child) {
                    return Text(
                      score.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // Pause Button with professional design
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.shade400,
                  Colors.blue.shade600,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: IconButton(
              onPressed: _pauseGame,
              icon: Icon(
                _isPaused ? Icons.play_arrow_rounded : Icons.pause_rounded,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlInstructions() {
    return Positioned(
      bottom: 30,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(150),
          borderRadius: BorderRadius.circular(15),
        ),
        child: const Text(
          '🎮 اضغط باستمرار للطيران • اترك الشاشة للسقوط',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildPauseOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.blue.shade400,
                    Colors.purple.shade600,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.pause_circle_filled_rounded,
                    color: Colors.white,
                    size: 80,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'PAUSED',
                    style: TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 30),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildPauseButton(
                        'RESUME',
                        Icons.play_arrow_rounded,
                        Colors.green,
                        _pauseGame,
                      ),
                      const SizedBox(width: 20),
                      _buildPauseButton(
                        'MENU',
                        Icons.home_rounded,
                        Colors.red,
                        _goToMainMenu,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameOverOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.9),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.red.shade400,
                    Colors.orange.shade600,
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withOpacity(0.4),
                    blurRadius: 25,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.sentiment_dissatisfied_rounded,
                    color: Colors.white,
                    size: 80,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'GAME OVER',
                    style: TextStyle(
                      fontSize: 42,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 15),
                  ValueListenableBuilder<int>(
                    valueListenable: game.scoreNotifier,
                    builder: (context, score, child) {
                      return Text(
                        'Final Score: $score',
                        style: const TextStyle(
                          fontSize: 24,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 35),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildGameOverButton(
                        'PLAY AGAIN',
                        Icons.refresh_rounded,
                        Colors.green,
                        () => game.resetGame(),
                      ),
                      const SizedBox(width: 20),
                      _buildGameOverButton(
                        'MAIN MENU',
                        Icons.home_rounded,
                        Colors.blue,
                        _goToMainMenu,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPauseButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
      ),
    );
  }

  Widget _buildGameOverButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }
}

// لعبة ArzaRush الاحترافية
class ProfessionalArzaRushGame extends FlameGame with HasCollisionDetection {
  // Game state
  final ValueNotifier<int> scoreNotifier = ValueNotifier<int>(0);
  final ValueNotifier<bool> gameOverNotifier = ValueNotifier<bool>(false);
  int _score = 0;
  bool _isGameOver = false;

  // Game components
  late ProfessionalPlayer player;
  late ProfessionalGround ground;
  late ProfessionalBackground background;

  // Game speed
  double gameSpeed = 250.0;
  final double maxGameSpeed = 500.0;

  // Timers for spawning
  late Timer obstacleTimer;
  late Timer coinTimer;
  late Timer powerUpTimer;
  late Timer airObstacleTimer; // للعوائق الجوية

  final Random _random = Random();

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // تشغيل الموسيقى الخلفية
    SoundManager.playBackgroundMusic(SoundManager.backgroundMusic);

    // Create background with parallax effect
    background = ProfessionalBackground();
    add(background);

    // Create ground with moving pattern
    ground = ProfessionalGround();
    add(ground);

    // Create player with animations
    player = ProfessionalPlayer();
    add(player);

    // Setup obstacle spawning with difficulty scaling
    obstacleTimer = Timer(
      2.5,
      repeat: true,
      onTick: _spawnObstacle,
    );

    // Setup coin spawning
    coinTimer = Timer(
      1.8,
      repeat: true,
      onTick: _spawnCoin,
    );

    // Setup power-up spawning
    powerUpTimer = Timer(
      8.0,
      repeat: true,
      onTick: _spawnPowerUp,
    );

    // Setup air obstacles spawning (طيور صاعقة وصواريخ طائرة)
    airObstacleTimer = Timer(
      3.0,
      repeat: true,
      onTick: _spawnAirObstacle,
    );

    // Start all timers
    obstacleTimer.start();
    coinTimer.start();
    powerUpTimer.start();
    airObstacleTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (_isGameOver) return;

    // Update timers
    obstacleTimer.update(dt);
    coinTimer.update(dt);
    powerUpTimer.update(dt);

    // Increase game speed gradually
    if (gameSpeed < maxGameSpeed) {
      gameSpeed += 15 * dt;
    }

    // Update score based on distance
    _score += (gameSpeed * dt / 15).round();
    scoreNotifier.value = _score;

    // Update difficulty - spawn obstacles faster as score increases
    if (_score > 500 && obstacleTimer.limit > 1.5) {
      obstacleTimer.limit = 1.5;
    }
    if (_score > 1000 && obstacleTimer.limit > 1.2) {
      obstacleTimer.limit = 1.2;
    }
  }

  void _spawnObstacle() {
    if (_isGameOver) return;

    final obstacle = ProfessionalObstacle();
    obstacle.position = Vector2(size.x + 50, size.y - 150);
    add(obstacle);
  }

  void _spawnCoin() {
    if (_isGameOver) return;

    final coin = ProfessionalCoin();
    coin.position = Vector2(
      size.x + 50,
      size.y - 200 - _random.nextDouble() * 100,
    );
    add(coin);
  }

  void _spawnPowerUp() {
    if (_isGameOver) return;

    final powerUp = ProfessionalPowerUp();
    powerUp.position = Vector2(
      size.x + 50,
      size.y - 180 - _random.nextDouble() * 80,
    );
    add(powerUp);
  }

  void _spawnAirObstacle() {
    if (_isGameOver) return;

    final airObstacle = AirObstacle();
    // العوائق الجوية تظهر في الجزء العلوي من الشاشة
    airObstacle.position = Vector2(
      size.x + 50,
      100 + _random.nextDouble() * 200, // في الجو بين 100-300 بكسل من الأعلى
    );
    add(airObstacle);
  }

  void jump() {
    if (!_isGameOver) {
      player.jump();
      HapticFeedback.lightImpact();
    }
  }

  // آلية الطيران الجديدة
  void startFlying() {
    if (!_isGameOver) {
      player.startFlying();
      HapticFeedback.lightImpact();
    }
  }

  void stopFlying() {
    if (!_isGameOver) {
      player.stopFlying();
    }
  }

  void collectCoin() {
    _score += 50;
    scoreNotifier.value = _score;
    HapticFeedback.lightImpact();
  }

  void collectPowerUp() {
    _score += 100;
    scoreNotifier.value = _score;
    // TODO: Add power-up effects
    HapticFeedback.mediumImpact();
  }

  void gameOver() {
    if (_isGameOver) return;

    _isGameOver = true;
    gameOverNotifier.value = true;

    // تشغيل صوت انتهاء اللعبة
    SoundManager.playSound(SoundManager.gameOverSound);

    // إيقاف الموسيقى الخلفية
    SoundManager.stopBackgroundMusic();

    obstacleTimer.stop();
    coinTimer.stop();
    powerUpTimer.stop();
    airObstacleTimer.stop();

    HapticFeedback.heavyImpact();
  }

  void resetGame() {
    _isGameOver = false;
    gameOverNotifier.value = false;
    _score = 0;
    scoreNotifier.value = 0;
    gameSpeed = 250.0;

    // إعادة تشغيل الموسيقى الخلفية
    SoundManager.playBackgroundMusic(SoundManager.backgroundMusic);

    // Remove all game objects
    children.whereType<ProfessionalObstacle>().forEach((obstacle) => obstacle.removeFromParent());
    children.whereType<ProfessionalCoin>().forEach((coin) => coin.removeFromParent());
    children.whereType<ProfessionalPowerUp>().forEach((powerUp) => powerUp.removeFromParent());
    children.whereType<AirObstacle>().forEach((airObstacle) => airObstacle.removeFromParent());
    children.whereType<AirProjectile>().forEach((projectile) => projectile.removeFromParent());

    // Reset player
    player.reset();

    // Reset timers
    obstacleTimer.limit = 2.5;
    obstacleTimer.start();
    coinTimer.start();
    powerUpTimer.start();
    airObstacleTimer.start();
  }
}

// Professional Player Component
class ProfessionalPlayer extends RectangleComponent
    with HasGameRef<ProfessionalArzaRushGame>, CollisionCallbacks {

  static const double playerWidth = 50.0;
  static const double playerHeight = 70.0;
  static const double jumpSpeed = -650.0; // قفز أقوى وأسرع
  static const double gravity = 1200.0; // جاذبية أقوى للهبوط السريع
  static const double flySpeed = -300.0; // سرعة الطيران المستمر

  double _velocityY = 0.0;
  double _groundY = 0.0;
  bool _isOnGround = true;
  bool _isFlying = false; // حالة الطيران المستمر

  // Animation properties
  late Timer _animationTimer;
  int _currentFrame = 0;
  final List<Color> _runColors = [
    Colors.blue.shade400,
    Colors.blue.shade500,
    Colors.blue.shade600,
  ];

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    size = Vector2(playerWidth, playerHeight);
    _groundY = gameRef.size.y - 150;
    position = Vector2(100, _groundY);

    // إزالة لون الخلفية - جعل الشخصية شفافة
    paint.color = Colors.transparent;

    add(RectangleHitbox());

    // Setup running animation
    _animationTimer = Timer(
      0.15,
      repeat: true,
      onTick: _updateAnimation,
    );
    _animationTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    _animationTimer.update(dt);

    // آلية الطيران الجديدة
    if (_isFlying) {
      // الطيران المستمر - البقاء في الأعلى
      _velocityY = flySpeed;
      position.y += _velocityY * dt;

      // منع الطيران أعلى من حد معين
      if (position.y < 100) {
        position.y = 100;
        _velocityY = 0;
      }

      _isOnGround = false;
    } else {
      // الجاذبية العادية عند عدم الطيران
      if (!_isOnGround) {
        _velocityY += gravity * dt;
        position.y += _velocityY * dt;

        if (position.y >= _groundY) {
          position.y = _groundY;
          _velocityY = 0.0;
          _isOnGround = true;
        }
      }

      // Ground bobbing effect when running
      if (_isOnGround) {
        position.y = _groundY + (sin(DateTime.now().millisecondsSinceEpoch / 150) * 2);
      }
    }
  }

  void jump() {
    if (_isOnGround) {
      _velocityY = jumpSpeed;
      _isOnGround = false;

      // تشغيل صوت القفز
      SoundManager.playSound(SoundManager.jumpSound);

      // تأثير بصري للقفز
      Future.delayed(const Duration(milliseconds: 200), () {
        // يمكن إضافة تأثيرات إضافية هنا
      });
    }
  }

  // دوال الطيران الجديدة
  void startFlying() {
    if (!_isFlying) {
      // تشغيل صوت الطيران عند البدء
      SoundManager.playSound(SoundManager.flySound);
    }
    _isFlying = true;
    _isOnGround = false;
  }

  void stopFlying() {
    if (_isFlying) {
      // تشغيل صوت الهبوط
      SoundManager.playSound(SoundManager.landSound);
    }
    _isFlying = false;
    // السماح للجاذبية بالعمل
  }

  void _updateAnimation() {
    _currentFrame = (_currentFrame + 1) % _runColors.length;
    // لا نحتاج لتغيير اللون بعد الآن لأن الشخصية شفافة
  }

  void reset() {
    position = Vector2(100, _groundY);
    _velocityY = 0.0;
    _isOnGround = true;
    _isFlying = false; // إيقاف الطيران
    _currentFrame = 0;
    paint.color = Colors.transparent; // الحفاظ على الشفافية
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is ProfessionalObstacle) {
      // تشغيل صوت الاصطدام
      SoundManager.playSound(SoundManager.crashSound);
      gameRef.gameOver();
      return true;
    } else if (other is AirObstacle) {
      // الاصطدام مع العوائق الجوية
      SoundManager.playSound(SoundManager.explosionSound);
      gameRef.gameOver();
      return true;
    } else if (other is AirProjectile) {
      // الاصطدام مع المقذوفات (البرق، النار، الصواريخ)
      other.removeFromParent();

      // تشغيل صوت حسب نوع المقذوف
      switch (other.type) {
        case ProjectileType.thunder:
          SoundManager.playSound(SoundManager.thunderSound);
          break;
        case ProjectileType.fire:
          SoundManager.playSound(SoundManager.fireSound);
          break;
        case ProjectileType.missile:
          SoundManager.playSound(SoundManager.explosionSound);
          break;
      }

      gameRef.gameOver();
      return true;
    } else if (other is ProfessionalCoin) {
      other.removeFromParent();
      // تشغيل صوت جمع العملة
      SoundManager.playSound(SoundManager.coinSound);
      gameRef.collectCoin();
      return false;
    } else if (other is ProfessionalPowerUp) {
      other.removeFromParent();
      // تشغيل صوت القوة الخاصة
      SoundManager.playSound(SoundManager.powerUpSound);
      gameRef.collectPowerUp();
      return false;
    }
    return true;
  }

  @override
  void render(Canvas canvas) {
    // لا نستدعي super.render(canvas) لتجنب رسم المكعب الأزرق

    // رسم شخصية كرتونية احترافية فقط
    _drawCartoonCharacter(canvas);
  }

  void _drawCartoonCharacter(Canvas canvas) {
    // الرأس (دائرة كبيرة)
    final headPaint = Paint()
      ..color = const Color(0xFFFFDBB5) // لون البشرة
      ..style = PaintingStyle.fill;

    final headCenter = Offset(size.x * 0.5, size.y * 0.3);
    canvas.drawCircle(headCenter, size.x * 0.35, headPaint);

    // الشعر
    final hairPaint = Paint()
      ..color = const Color(0xFF8B4513) // بني
      ..style = PaintingStyle.fill;

    final hairPath = Path();
    hairPath.addArc(
      Rect.fromCenter(
        center: headCenter,
        width: size.x * 0.7,
        height: size.x * 0.5,
      ),
      -3.14159,
      3.14159,
    );
    canvas.drawPath(hairPath, hairPaint);

    // العيون (تنظر إلى الأمام نحو العوائق)
    final eyeWhitePaint = Paint()..color = Colors.white;
    final eyePupilPaint = Paint()..color = Colors.black;

    // العين اليسرى
    final leftEyeCenter = Offset(size.x * 0.35, size.y * 0.25);
    canvas.drawCircle(leftEyeCenter, 6, eyeWhitePaint);
    // البؤبؤ ينظر إلى الأمام (يمين الشاشة)
    canvas.drawCircle(Offset(leftEyeCenter.dx + 2, leftEyeCenter.dy), 3, eyePupilPaint);

    // العين اليمنى
    final rightEyeCenter = Offset(size.x * 0.65, size.y * 0.25);
    canvas.drawCircle(rightEyeCenter, 6, eyeWhitePaint);
    // البؤبؤ ينظر إلى الأمام (يمين الشاشة)
    canvas.drawCircle(Offset(rightEyeCenter.dx + 2, rightEyeCenter.dy), 3, eyePupilPaint);

    // الأنف
    final nosePaint = Paint()
      ..color = const Color(0xFFFFB6C1) // وردي فاتح
      ..style = PaintingStyle.fill;
    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.32), 2, nosePaint);

    // الفم (ابتسامة)
    final mouthPaint = Paint()
      ..color = const Color(0xFFFF69B4) // وردي
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    final mouthPath = Path();
    mouthPath.moveTo(size.x * 0.4, size.y * 0.38);
    mouthPath.quadraticBezierTo(
      size.x * 0.5, size.y * 0.42,
      size.x * 0.6, size.y * 0.38,
    );
    canvas.drawPath(mouthPath, mouthPaint);

    // الجسم
    final bodyPaint = Paint()
      ..color = Colors.blue.shade600
      ..style = PaintingStyle.fill;

    final bodyRect = Rect.fromLTWH(
      size.x * 0.2,
      size.y * 0.5,
      size.x * 0.6,
      size.y * 0.35,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(bodyRect, const Radius.circular(8)),
      bodyPaint,
    );

    // الأذرع (في وضعية الجري إلى الأمام)
    final armPaint = Paint()
      ..color = const Color(0xFFFFDBB5) // لون البشرة
      ..style = PaintingStyle.fill;

    // حركة الأذرع أثناء الجري
    final armSwing = sin(DateTime.now().millisecondsSinceEpoch / 200) * 10;

    // الذراع اليسرى (تتحرك للأمام والخلف)
    canvas.drawCircle(Offset(size.x * 0.15, size.y * 0.6), 8, armPaint);
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.15, size.y * 0.6 + armSwing, size.x * 0.2, 6),
      armPaint,
    );

    // الذراع اليمنى (تتحرك عكس اليسرى)
    canvas.drawCircle(Offset(size.x * 0.85, size.y * 0.6), 8, armPaint);
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.65, size.y * 0.6 - armSwing, size.x * 0.2, 6),
      armPaint,
    );

    // الأرجل (في وضعية الجري إلى الأمام)
    final legPaint = Paint()
      ..color = Colors.blue.shade800
      ..style = PaintingStyle.fill;

    // حركة الأرجل أثناء الجري
    final legSwing = sin(DateTime.now().millisecondsSinceEpoch / 150) * 8;

    // الرجل اليسرى (تتحرك للأمام والخلف)
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.35 + legSwing, size.y * 0.85, 8, size.y * 0.15),
      legPaint,
    );

    // الرجل اليمنى (تتحرك عكس اليسرى)
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.57 - legSwing, size.y * 0.85, 8, size.y * 0.15),
      legPaint,
    );

    // الزلاجة الطائرة (Hoverboard)
    if (_isFlying) {
      _drawHoverboard(canvas);
    } else {
      // الأحذية العادية عند المشي (تتحرك مع الأرجل)
      final shoePaint = Paint()
        ..color = Colors.brown.shade800
        ..style = PaintingStyle.fill;

      // حركة الأحذية مع الأرجل
      final legSwing = sin(DateTime.now().millisecondsSinceEpoch / 150) * 8;

      // الحذاء الأيسر (يتحرك مع الرجل اليسرى)
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(size.x * 0.32 + legSwing, size.y * 0.95, 18, 8),
          const Radius.circular(4),
        ),
        shoePaint,
      );

      // الحذاء الأيمن (يتحرك مع الرجل اليمنى)
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(size.x * 0.54 - legSwing, size.y * 0.95, 18, 8),
          const Radius.circular(4),
        ),
        shoePaint,
      );
    }

    // تأثير الحركة (خطوط السرعة)
    if (!_isOnGround) {
      final speedLinePaint = Paint()
        ..color = Colors.white.withAlpha(150)
        ..strokeWidth = 2;

      for (int i = 0; i < 3; i++) {
        canvas.drawLine(
          Offset(size.x * 0.1, size.y * 0.3 + i * 8),
          Offset(size.x * 0.2, size.y * 0.3 + i * 8),
          speedLinePaint,
        );
      }
    }

    // تأثير الهبوط (غبار)
    if (_isOnGround && _velocityY == 0) {
      final dustPaint = Paint()
        ..color = Colors.brown.withAlpha(100)
        ..style = PaintingStyle.fill;

      for (int i = 0; i < 5; i++) {
        canvas.drawCircle(
          Offset(
            size.x * 0.2 + i * 8 + sin(DateTime.now().millisecondsSinceEpoch / 100 + i) * 3,
            size.y + 5,
          ),
          2 + sin(DateTime.now().millisecondsSinceEpoch / 150 + i) * 1,
          dustPaint,
        );
      }
    }
  }

  // رسم الزلاجة الطائرة
  void _drawHoverboard(Canvas canvas) {
    // جسم الزلاجة
    final hoverboardPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          Colors.cyan.shade400,
          Colors.blue.shade600,
          Colors.purple.shade600,
        ],
      ).createShader(Rect.fromLTWH(size.x * 0.1, size.y + 5, size.x * 0.8, 12));

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.1, size.y + 5, size.x * 0.8, 12),
        const Radius.circular(6),
      ),
      hoverboardPaint,
    );

    // تأثير الطاقة تحت الزلاجة
    final energyPaint = Paint()
      ..color = Colors.cyan.withAlpha(150)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.15, size.y + 18, size.x * 0.7, 6),
        const Radius.circular(3),
      ),
      energyPaint,
    );

    // جزيئات الطاقة المتحركة
    final particlePaint = Paint()..color = Colors.cyan.shade200;

    for (int i = 0; i < 5; i++) {
      final x = size.x * 0.2 + i * (size.x * 0.6 / 4);
      final y = size.y + 20 + sin(DateTime.now().millisecondsSinceEpoch / 100 + i) * 3;
      canvas.drawCircle(Offset(x, y), 2, particlePaint);
    }

    // أضواء LED على الزلاجة
    final ledPaint = Paint()..color = Colors.white;
    canvas.drawCircle(Offset(size.x * 0.2, size.y + 11), 1.5, ledPaint);
    canvas.drawCircle(Offset(size.x * 0.8, size.y + 11), 1.5, ledPaint);

    // خطوط الطاقة على الجانبين
    final energyLinePaint = Paint()
      ..color = Colors.cyan.shade300
      ..strokeWidth = 2;

    canvas.drawLine(
      Offset(size.x * 0.15, size.y + 8),
      Offset(size.x * 0.85, size.y + 8),
      energyLinePaint,
    );

    canvas.drawLine(
      Offset(size.x * 0.15, size.y + 14),
      Offset(size.x * 0.85, size.y + 14),
      energyLinePaint,
    );
  }
}

// Professional Background Component
class ProfessionalBackground extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  late List<CloudComponent> clouds;
  late List<BuildingComponent> buildings;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    size = gameRef.size;
    position = Vector2.zero();

    // Initialize clouds
    clouds = List.generate(5, (index) => CloudComponent(
      position: Vector2(
        index * 150.0 + Random().nextDouble() * 100,
        50.0 + Random().nextDouble() * 100,
      ),
      speed: 0.3 + Random().nextDouble() * 0.2,
    ));

    // Initialize buildings
    buildings = List.generate(8, (index) => BuildingComponent(
      position: Vector2(
        index * 80.0 + Random().nextDouble() * 50,
        size.y - 200 - Random().nextDouble() * 100,
      ),
      height: 100.0 + Random().nextDouble() * 150,
      speed: 0.5 + Random().nextDouble() * 0.3,
    ));
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Update clouds
    for (final cloud in clouds) {
      cloud.update(dt, gameRef.gameSpeed);
      if (cloud.position.x > size.x + 100) {
        cloud.position.x = -100;
      }
    }

    // Update buildings
    for (final building in buildings) {
      building.update(dt, gameRef.gameSpeed);
      if (building.position.x > size.x + 100) {
        building.position.x = -100;
      }
    }
  }

  @override
  void render(Canvas canvas) {
    // Sky gradient
    final rect = Rect.fromLTWH(0, 0, size.x, size.y);
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF87CEEB), // Sky blue
        const Color(0xFF98FB98), // Light green
        const Color(0xFFDEB887), // Burlywood
      ],
    );

    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // Render buildings
    for (final building in buildings) {
      building.render(canvas);
    }

    // Render clouds
    for (final cloud in clouds) {
      cloud.render(canvas);
    }
  }
}

// Helper classes for background elements
class CloudComponent {
  Vector2 position;
  final double speed;

  CloudComponent({required this.position, required this.speed});

  void update(double dt, double gameSpeed) {
    position.x += speed * gameSpeed * dt * 0.1;
  }

  void render(Canvas canvas) {
    final cloudPaint = Paint()..color = Colors.white.withOpacity(0.8);

    canvas.drawCircle(Offset(position.x, position.y), 25, cloudPaint);
    canvas.drawCircle(Offset(position.x + 20, position.y), 30, cloudPaint);
    canvas.drawCircle(Offset(position.x + 40, position.y), 25, cloudPaint);
    canvas.drawCircle(Offset(position.x + 20, position.y - 15), 20, cloudPaint);
  }
}

class BuildingComponent {
  Vector2 position;
  final double height;
  final double speed;

  BuildingComponent({
    required this.position,
    required this.height,
    required this.speed,
  });

  void update(double dt, double gameSpeed) {
    position.x += speed * gameSpeed * dt * 0.1;
  }

  void render(Canvas canvas) {
    final buildingPaint = Paint()..color = Colors.grey.withOpacity(0.4);

    canvas.drawRect(
      Rect.fromLTWH(position.x, position.y, 60, height),
      buildingPaint,
    );

    // Windows
    final windowPaint = Paint()..color = Colors.yellow.withOpacity(0.6);
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < (height / 30).floor(); j++) {
        if (Random().nextBool()) {
          canvas.drawRect(
            Rect.fromLTWH(
              position.x + 10 + i * 15,
              position.y + 10 + j * 25,
              8,
              12,
            ),
            windowPaint,
          );
        }
      }
    }
  }
}

// Professional Ground Component
class ProfessionalGround extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  late List<Vector2> trackMarks;
  double _scrollOffset = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    size = Vector2(gameRef.size.x, 50);
    position = Vector2(0, gameRef.size.y - 50);
    paint.color = Colors.brown.shade600;

    // Initialize track marks for moving effect
    trackMarks = List.generate(20, (index) => Vector2(
      index * 40.0,
      gameRef.size.y - 75,
    ));
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Update scroll offset for moving ground effect
    _scrollOffset += gameRef.gameSpeed * dt;

    // Reset offset to prevent overflow
    if (_scrollOffset > 40) {
      _scrollOffset -= 40;
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    // Draw track marks
    final markPaint = Paint()
      ..color = Colors.yellow
      ..strokeWidth = 3;

    for (int i = 0; i < trackMarks.length; i++) {
      final x = (trackMarks[i].x - _scrollOffset) % (size.x + 40);
      canvas.drawLine(
        Offset(x, trackMarks[i].y),
        Offset(x, trackMarks[i].y + 10),
        markPaint,
      );
    }
  }
}

// أنواع العوائق الجديدة
enum ObstacleType {
  crate,      // صندوق خشبي
  airplane,   // طائرة
  missile,    // صاروخ
  spy,        // جاسوس
}

// Professional Obstacle Component
class ProfessionalObstacle extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double obstacleWidth = 45.0;
  static const double obstacleHeight = 85.0;

  late Color _color;
  late Timer _warningTimer;
  bool _showWarning = false;
  late ObstacleType _type;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // اختيار نوع عائق عشوائي
    final random = Random();
    _type = ObstacleType.values[random.nextInt(ObstacleType.values.length)];

    // تحديد الحجم واللون حسب النوع
    _setupObstacleByType();

    add(RectangleHitbox());

    // Warning blink effect
    _warningTimer = Timer(
      0.3,
      repeat: true,
      onTick: () {
        _showWarning = !_showWarning;
      },
    );
    _warningTimer.start();
  }

  void _setupObstacleByType() {
    switch (_type) {
      case ObstacleType.crate:
        size = Vector2(obstacleWidth, obstacleHeight);
        _color = Colors.brown.shade600;
        break;
      case ObstacleType.airplane:
        size = Vector2(80, 40); // طائرة أوسع وأقصر
        _color = Colors.blue.shade600;
        break;
      case ObstacleType.missile:
        size = Vector2(30, 100); // صاروخ رفيع وطويل
        _color = Colors.red.shade600;
        break;
      case ObstacleType.spy:
        size = Vector2(40, 60); // جاسوس بحجم متوسط
        _color = Colors.black;
        break;
    }
    paint.color = _color;
  }

  @override
  void update(double dt) {
    super.update(dt);

    _warningTimer.update(dt);

    // Move obstacle to the left
    position.x -= gameRef.gameSpeed * dt;

    // Remove when off screen
    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    // رسم العائق حسب النوع
    switch (_type) {
      case ObstacleType.crate:
        _drawWoodenCrate(canvas);
        break;
      case ObstacleType.airplane:
        _drawAirplane(canvas);
        break;
      case ObstacleType.missile:
        _drawMissile(canvas);
        break;
      case ObstacleType.spy:
        _drawSpy(canvas);
        break;
    }

    // Warning triangle on top
    if (_showWarning) {
      _drawWarningSign(canvas);
    }
  }

  void _drawWoodenCrate(Canvas canvas) {
    // الجسم الرئيسي للصندوق
    final cratePaint = Paint()
      ..color = const Color(0xFF8B4513) // بني خشبي
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.x, size.y),
        const Radius.circular(4),
      ),
      cratePaint,
    );

    // خطوط الخشب
    final woodLinePaint = Paint()
      ..color = const Color(0xFF654321) // بني داكن
      ..strokeWidth = 2;

    for (int i = 1; i < 4; i++) {
      final y = size.y * i / 4;
      canvas.drawLine(
        Offset(2, y),
        Offset(size.x - 2, y),
        woodLinePaint,
      );
    }

    // خطوط عمودية
    for (int i = 1; i < 3; i++) {
      final x = size.x * i / 3;
      canvas.drawLine(
        Offset(x, 2),
        Offset(x, size.y - 2),
        woodLinePaint,
      );
    }

    // مسامير معدنية
    final nailPaint = Paint()
      ..color = const Color(0xFF708090) // رمادي معدني
      ..style = PaintingStyle.fill;

    // مسامير في الزوايا
    canvas.drawCircle(Offset(5, 5), 2, nailPaint);
    canvas.drawCircle(Offset(size.x - 5, 5), 2, nailPaint);
    canvas.drawCircle(Offset(5, size.y - 5), 2, nailPaint);
    canvas.drawCircle(Offset(size.x - 5, size.y - 5), 2, nailPaint);

    // ظل
    final shadowPaint = Paint()
      ..color = Colors.black.withAlpha(50)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(2, 2, size.x, size.y),
        const Radius.circular(4),
      ),
      shadowPaint,
    );
  }

  void _drawWarningSign(Canvas canvas) {
    final trianglePaint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;

    final trianglePath = Path();
    trianglePath.moveTo(size.x * 0.5, -20);
    trianglePath.lineTo(size.x * 0.2, -5);
    trianglePath.lineTo(size.x * 0.8, -5);
    trianglePath.close();
    canvas.drawPath(trianglePath, trianglePaint);

    // إطار المثلث
    final borderPaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawPath(trianglePath, borderPaint);

    // علامة التعجب
    final exclamationPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(size.x * 0.5, -16),
      Offset(size.x * 0.5, -10),
      exclamationPaint,
    );
    canvas.drawCircle(
      Offset(size.x * 0.5, -7),
      1.5,
      Paint()..color = Colors.red,
    );
  }

  // رسم طائرة
  void _drawAirplane(Canvas canvas) {
    // جسم الطائرة
    final bodyPaint = Paint()..color = Colors.blue.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, size.y * 0.3, size.x, size.y * 0.4),
        const Radius.circular(8),
      ),
      bodyPaint,
    );

    // الأجنحة
    final wingPaint = Paint()..color = Colors.blue.shade400;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.1, size.y * 0.5, size.x * 0.8, size.y * 0.2),
        const Radius.circular(4),
      ),
      wingPaint,
    );

    // المروحة
    final propellerPaint = Paint()..color = Colors.grey.shade600;
    canvas.drawCircle(
      Offset(size.x * 0.9, size.y * 0.5),
      8,
      propellerPaint,
    );

    // النوافذ
    final windowPaint = Paint()..color = Colors.lightBlue.shade200;
    for (int i = 0; i < 3; i++) {
      canvas.drawCircle(
        Offset(size.x * 0.2 + i * 15, size.y * 0.4),
        4,
        windowPaint,
      );
    }
  }

  // رسم صاروخ
  void _drawMissile(Canvas canvas) {
    // جسم الصاروخ
    final bodyPaint = Paint()..color = Colors.red.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.2, 0, size.x * 0.6, size.y * 0.8),
        const Radius.circular(6),
      ),
      bodyPaint,
    );

    // رأس الصاروخ (مثلث)
    final nosePaint = Paint()..color = Colors.red.shade800;
    final nosePath = Path();
    nosePath.moveTo(size.x * 0.5, 0);
    nosePath.lineTo(size.x * 0.2, size.y * 0.2);
    nosePath.lineTo(size.x * 0.8, size.y * 0.2);
    nosePath.close();
    canvas.drawPath(nosePath, nosePaint);

    // الزعانف
    final finPaint = Paint()..color = Colors.orange.shade600;
    final finPath = Path();
    finPath.moveTo(size.x * 0.1, size.y * 0.7);
    finPath.lineTo(size.x * 0.2, size.y * 0.9);
    finPath.lineTo(size.x * 0.8, size.y * 0.9);
    finPath.lineTo(size.x * 0.9, size.y * 0.7);
    finPath.close();
    canvas.drawPath(finPath, finPaint);

    // اللهب (تأثير الدفع)
    final flamePaint = Paint()..color = Colors.orange;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.3, size.y * 0.9, size.x * 0.4, size.y * 0.1),
        const Radius.circular(2),
      ),
      flamePaint,
    );
  }

  // رسم جاسوس
  void _drawSpy(Canvas canvas) {
    // الجسم
    final bodyPaint = Paint()..color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.2, size.y * 0.4, size.x * 0.6, size.y * 0.5),
        const Radius.circular(8),
      ),
      bodyPaint,
    );

    // الرأس
    final headPaint = Paint()..color = const Color(0xFFFFDBB5); // لون البشرة
    canvas.drawCircle(
      Offset(size.x * 0.5, size.y * 0.25),
      size.x * 0.2,
      headPaint,
    );

    // القبعة
    final hatPaint = Paint()..color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.3, size.y * 0.1, size.x * 0.4, size.y * 0.2),
        const Radius.circular(4),
      ),
      hatPaint,
    );

    // النظارات السوداء
    final glassesPaint = Paint()..color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.35, size.y * 0.22, size.x * 0.3, size.y * 0.08),
        const Radius.circular(2),
      ),
      glassesPaint,
    );

    // الأذرع
    final armPaint = Paint()..color = const Color(0xFFFFDBB5);
    canvas.drawCircle(Offset(size.x * 0.1, size.y * 0.6), 6, armPaint);
    canvas.drawCircle(Offset(size.x * 0.9, size.y * 0.6), 6, armPaint);

    // الأرجل
    final legPaint = Paint()..color = Colors.black;
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.35, size.y * 0.85, 6, size.y * 0.15),
      legPaint,
    );
    canvas.drawRect(
      Rect.fromLTWH(size.x * 0.55, size.y * 0.85, 6, size.y * 0.15),
      legPaint,
    );
  }
}

// Professional Coin Component
class ProfessionalCoin extends CircleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double coinRadius = 18.0;

  late Timer _sparkleTimer;
  bool _isSparkleVisible = false;
  double _rotationAngle = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    radius = coinRadius;
    paint.color = Colors.amber;

    add(CircleHitbox());

    // Sparkle animation
    _sparkleTimer = Timer(
      0.4,
      repeat: true,
      onTick: () {
        _isSparkleVisible = !_isSparkleVisible;
      },
    );
    _sparkleTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    _sparkleTimer.update(dt);

    // Move coin to the left
    position.x -= gameRef.gameSpeed * dt;

    // Rotate coin
    _rotationAngle += 6 * dt;

    // Bobbing effect
    position.y += sin(DateTime.now().millisecondsSinceEpoch / 200) * 0.5;

    // Remove when off screen
    if (position.x + radius < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    canvas.save();
    canvas.rotate(_rotationAngle);

    // رسم عملة ذهبية احترافية
    _drawProfessionalCoin(canvas);

    // Sparkles
    if (_isSparkleVisible) {
      _drawSparkles(canvas);
    }

    canvas.restore();
  }

  void _drawProfessionalCoin(Canvas canvas) {
    // الظل
    final shadowPaint = Paint()
      ..color = Colors.black.withAlpha(100)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    canvas.drawCircle(const Offset(2, 2), radius, shadowPaint);

    // الجسم الرئيسي للعملة (تدرج ذهبي)
    final coinGradient = RadialGradient(
      colors: [
        const Color(0xFFFFD700), // ذهبي فاتح
        const Color(0xFFB8860B), // ذهبي داكن
        const Color(0xFF8B7355), // بني ذهبي
      ],
      stops: const [0.0, 0.7, 1.0],
    );

    final coinPaint = Paint()
      ..shader = coinGradient.createShader(
        Rect.fromCircle(center: Offset.zero, radius: radius),
      );
    canvas.drawCircle(Offset.zero, radius, coinPaint);

    // الحافة الخارجية
    final outerBorderPaint = Paint()
      ..color = const Color(0xFF8B7355)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawCircle(Offset.zero, radius, outerBorderPaint);

    // الحافة الداخلية
    final innerBorderPaint = Paint()
      ..color = const Color(0xFFFFD700)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    canvas.drawCircle(Offset.zero, radius * 0.8, innerBorderPaint);

    // رمز الدولار المحسن
    _drawDollarSign(canvas);

    // نقاط زخرفية حول الحافة
    _drawDecorativePoints(canvas);
  }

  void _drawDollarSign(Canvas canvas) {
    final dollarPaint = Paint()
      ..color = const Color(0xFF8B4513) // بني داكن
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // الخط العمودي
    canvas.drawLine(
      const Offset(0, -10),
      const Offset(0, 10),
      dollarPaint,
    );

    // الجزء العلوي من S
    final upperPath = Path();
    upperPath.moveTo(-6, -6);
    upperPath.quadraticBezierTo(0, -10, 6, -6);
    upperPath.quadraticBezierTo(0, -2, -6, 2);
    canvas.drawPath(upperPath, dollarPaint);

    // الجزء السفلي من S
    final lowerPath = Path();
    lowerPath.moveTo(6, -2);
    lowerPath.quadraticBezierTo(0, 2, -6, 2);
    lowerPath.quadraticBezierTo(0, 6, 6, 6);
    canvas.drawPath(lowerPath, dollarPaint);
  }

  void _drawDecorativePoints(Canvas canvas) {
    final pointPaint = Paint()
      ..color = const Color(0xFFFFD700)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 8; i++) {
      final angle = (i * 2 * pi) / 8;
      final x = (radius * 0.9) * cos(angle);
      final y = (radius * 0.9) * sin(angle);
      canvas.drawCircle(Offset(x, y), 1.5, pointPaint);
    }
  }

  void _drawStar(Canvas canvas, Paint paint) {
    final path = Path();
    final outerRadius = radius * 0.4;
    final innerRadius = radius * 0.2;

    for (int i = 0; i < 10; i++) {
      final angle = (i * pi) / 5;
      final r = i.isEven ? outerRadius : innerRadius;
      final x = r * cos(angle);
      final y = r * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    canvas.drawPath(path, paint);
  }

  void _drawSparkles(Canvas canvas) {
    final sparklePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2;

    final sparklePositions = [
      Offset(-radius * 1.3, -radius * 0.9),
      Offset(radius * 1.3, -radius * 0.9),
      Offset(-radius * 1.3, radius * 0.9),
      Offset(radius * 1.3, radius * 0.9),
    ];

    for (final pos in sparklePositions) {
      canvas.drawLine(
        Offset(pos.dx - 4, pos.dy),
        Offset(pos.dx + 4, pos.dy),
        sparklePaint,
      );
      canvas.drawLine(
        Offset(pos.dx, pos.dy - 4),
        Offset(pos.dx, pos.dy + 4),
        sparklePaint,
      );
    }
  }
}

// Professional PowerUp Component
class ProfessionalPowerUp extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double powerUpSize = 35.0;

  late Timer _pulseTimer;
  double _pulseScale = 1.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    size = Vector2(powerUpSize, powerUpSize);
    paint.color = Colors.purple;

    add(RectangleHitbox());

    // Pulse animation
    _pulseTimer = Timer(
      0.5,
      repeat: true,
      onTick: () {
        _pulseScale = _pulseScale == 1.0 ? 1.2 : 1.0;
      },
    );
    _pulseTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    _pulseTimer.update(dt);

    // Move power-up to the left
    position.x -= gameRef.gameSpeed * dt;

    // Floating effect
    position.y += sin(DateTime.now().millisecondsSinceEpoch / 300) * 1;

    // Remove when off screen
    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    canvas.save();
    canvas.scale(_pulseScale);

    super.render(canvas);

    // Power-up symbol (lightning bolt)
    final symbolPaint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.x * 0.3, 0);
    path.lineTo(size.x * 0.7, size.y * 0.4);
    path.lineTo(size.x * 0.5, size.y * 0.4);
    path.lineTo(size.x * 0.7, size.y);
    path.lineTo(size.x * 0.3, size.y * 0.6);
    path.lineTo(size.x * 0.5, size.y * 0.6);
    path.close();

    canvas.drawPath(path, symbolPaint);

    canvas.restore();
  }
}

// العوائق الجوية (طيور صاعقة وصواريخ طائرة)
class AirObstacle extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  static const double airObstacleWidth = 60.0;
  static const double airObstacleHeight = 50.0;

  late AirObstacleType _type;
  late Timer _attackTimer;
  late Timer _animationTimer;
  bool _isAttacking = false;
  double _animationFrame = 0.0;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // اختيار نوع عائق جوي عشوائي
    final random = Random();
    _type = AirObstacleType.values[random.nextInt(AirObstacleType.values.length)];

    _setupAirObstacleByType();

    add(RectangleHitbox());

    // Timer للهجوم (إطلاق الصواريخ أو الأحجار)
    _attackTimer = Timer(
      2.0 + random.nextDouble() * 2.0, // هجوم كل 2-4 ثواني
      repeat: true,
      onTick: _performAttack,
    );
    _attackTimer.start();

    // Timer للحركة والرسوم المتحركة
    _animationTimer = Timer(
      0.1,
      repeat: true,
      onTick: () {
        _animationFrame += 0.1;
      },
    );
    _animationTimer.start();
  }

  void _setupAirObstacleByType() {
    switch (_type) {
      case AirObstacleType.thunderBird:
        size = Vector2(airObstacleWidth, airObstacleHeight);
        paint.color = Colors.purple.shade600;
        break;
      case AirObstacleType.fireBird:
        size = Vector2(airObstacleWidth, airObstacleHeight);
        paint.color = Colors.red.shade600;
        break;
      case AirObstacleType.flyingMissile:
        size = Vector2(50, 30);
        paint.color = Colors.orange.shade600;
        break;
    }
  }

  @override
  void update(double dt) {
    super.update(dt);

    _attackTimer.update(dt);
    _animationTimer.update(dt);

    // حركة العائق الجوي
    position.x -= gameRef.gameSpeed * dt;

    // حركة تموجية في الهواء
    position.y += sin(_animationFrame * 3) * 0.5;

    // إزالة العائق عند الخروج من الشاشة
    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }

  void _performAttack() {
    if (gameRef._isGameOver) return;

    _isAttacking = true;

    // إنشاء مقذوف حسب نوع العائق مع الأصوات
    switch (_type) {
      case AirObstacleType.thunderBird:
        SoundManager.playSound(SoundManager.thunderSound);
        _createThunderBolt();
        break;
      case AirObstacleType.fireBird:
        SoundManager.playSound(SoundManager.fireSound);
        _createFireBall();
        break;
      case AirObstacleType.flyingMissile:
        SoundManager.playSound(SoundManager.missileSound);
        _createMissileAttack();
        break;
    }

    // إيقاف الهجوم بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 500), () {
      _isAttacking = false;
    });
  }

  void _createThunderBolt() {
    final thunderBolt = AirProjectile(
      type: ProjectileType.thunder,
      startPosition: Vector2(position.x, position.y + size.y),
      targetY: gameRef.size.y - 150, // نحو الأرض
    );
    gameRef.add(thunderBolt);
  }

  void _createFireBall() {
    final fireBall = AirProjectile(
      type: ProjectileType.fire,
      startPosition: Vector2(position.x, position.y + size.y),
      targetY: gameRef.size.y - 150,
    );
    gameRef.add(fireBall);
  }

  void _createMissileAttack() {
    final missile = AirProjectile(
      type: ProjectileType.missile,
      startPosition: Vector2(position.x, position.y + size.y),
      targetY: gameRef.size.y - 150,
    );
    gameRef.add(missile);
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    switch (_type) {
      case AirObstacleType.thunderBird:
        _drawThunderBird(canvas);
        break;
      case AirObstacleType.fireBird:
        _drawFireBird(canvas);
        break;
      case AirObstacleType.flyingMissile:
        _drawFlyingMissile(canvas);
        break;
    }
  }

  void _drawThunderBird(Canvas canvas) {
    // جسم الطائر
    final bodyPaint = Paint()..color = Colors.purple.shade600;
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.2, size.y * 0.3, size.x * 0.6, size.y * 0.4),
      bodyPaint,
    );

    // الأجنحة (متحركة)
    final wingPaint = Paint()..color = Colors.purple.shade400;
    final wingOffset = sin(_animationFrame * 10) * 5;

    // الجناح الأيسر
    canvas.drawOval(
      Rect.fromLTWH(0, size.y * 0.2 + wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // الجناح الأيمن
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.7, size.y * 0.2 - wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // العيون المتوهجة
    final eyePaint = Paint()..color = Colors.yellow;
    canvas.drawCircle(Offset(size.x * 0.4, size.y * 0.4), 3, eyePaint);
    canvas.drawCircle(Offset(size.x * 0.6, size.y * 0.4), 3, eyePaint);

    // البرق حول الطائر
    if (_isAttacking) {
      final lightningPaint = Paint()
        ..color = Colors.yellow
        ..strokeWidth = 2;

      for (int i = 0; i < 3; i++) {
        final startX = size.x * 0.5 + sin(_animationFrame * 5 + i) * 20;
        final startY = size.y * 0.5 + cos(_animationFrame * 5 + i) * 15;
        canvas.drawLine(
          Offset(startX, startY),
          Offset(startX + 10, startY + 15),
          lightningPaint,
        );
      }
    }
  }

  void _drawFireBird(Canvas canvas) {
    // جسم الطائر الناري
    final bodyPaint = Paint()
      ..shader = RadialGradient(
        colors: [Colors.red, Colors.orange, Colors.yellow],
      ).createShader(Rect.fromLTWH(0, 0, size.x, size.y));

    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.2, size.y * 0.3, size.x * 0.6, size.y * 0.4),
      bodyPaint,
    );

    // الأجنحة النارية
    final wingPaint = Paint()..color = Colors.orange.shade600;
    final wingOffset = sin(_animationFrame * 8) * 3;

    canvas.drawOval(
      Rect.fromLTWH(0, size.y * 0.2 + wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.7, size.y * 0.2 - wingOffset, size.x * 0.3, size.y * 0.6),
      wingPaint,
    );

    // اللهب حول الطائر
    final flamePaint = Paint()..color = Colors.red.withAlpha(150);
    for (int i = 0; i < 5; i++) {
      final flameX = size.x * 0.5 + sin(_animationFrame * 4 + i) * 15;
      final flameY = size.y * 0.5 + cos(_animationFrame * 4 + i) * 10;
      canvas.drawCircle(Offset(flameX, flameY), 3, flamePaint);
    }
  }

  void _drawFlyingMissile(Canvas canvas) {
    // جسم الصاروخ الطائر
    final missilePaint = Paint()..color = Colors.orange.shade600;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, size.y * 0.3, size.x * 0.8, size.y * 0.4),
        const Radius.circular(5),
      ),
      missilePaint,
    );

    // رأس الصاروخ
    final nosePaint = Paint()..color = Colors.red.shade600;
    final nosePath = Path();
    nosePath.moveTo(size.x * 0.8, size.y * 0.5);
    nosePath.lineTo(size.x, size.y * 0.3);
    nosePath.lineTo(size.x, size.y * 0.7);
    nosePath.close();
    canvas.drawPath(nosePath, nosePaint);

    // اللهب الخلفي
    final flamePaint = Paint()..color = Colors.blue.shade400;
    canvas.drawOval(
      Rect.fromLTWH(-10, size.y * 0.4, 15, size.y * 0.2),
      flamePaint,
    );
  }
}

// أنواع العوائق الجوية
enum AirObstacleType {
  thunderBird,   // طائر البرق
  fireBird,      // طائر النار
  flyingMissile, // صاروخ طائر
}

// أنواع المقذوفات
enum ProjectileType {
  thunder,  // برق
  fire,     // نار
  missile,  // صاروخ
}

// المقذوفات التي تطلقها العوائق الجوية
class AirProjectile extends RectangleComponent with HasGameRef<ProfessionalArzaRushGame> {
  final ProjectileType type;
  final Vector2 startPosition;
  final double targetY;

  late double _speed;
  late Timer _effectTimer;
  double _effectFrame = 0.0;

  AirProjectile({
    required this.type,
    required this.startPosition,
    required this.targetY,
  });

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    position = startPosition.clone();

    switch (type) {
      case ProjectileType.thunder:
        size = Vector2(8, 30);
        _speed = 400;
        paint.color = Colors.yellow;
        break;
      case ProjectileType.fire:
        size = Vector2(12, 12);
        _speed = 300;
        paint.color = Colors.red;
        break;
      case ProjectileType.missile:
        size = Vector2(6, 20);
        _speed = 500;
        paint.color = Colors.orange;
        break;
    }

    add(RectangleHitbox());

    _effectTimer = Timer(
      0.05,
      repeat: true,
      onTick: () {
        _effectFrame += 0.05;
      },
    );
    _effectTimer.start();
  }

  @override
  void update(double dt) {
    super.update(dt);

    _effectTimer.update(dt);

    // حركة المقذوف نحو الأسفل
    position.y += _speed * dt;

    // إزالة المقذوف عند وصوله للأرض أو خروجه من الشاشة
    if (position.y > gameRef.size.y || position.x < -50) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);

    switch (type) {
      case ProjectileType.thunder:
        _drawThunderBolt(canvas);
        break;
      case ProjectileType.fire:
        _drawFireBall(canvas);
        break;
      case ProjectileType.missile:
        _drawMissileProjectile(canvas);
        break;
    }
  }

  void _drawThunderBolt(Canvas canvas) {
    final lightningPaint = Paint()
      ..color = Colors.yellow
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    // رسم البرق المتعرج
    final path = Path();
    path.moveTo(size.x * 0.5, 0);
    path.lineTo(size.x * 0.3, size.y * 0.3);
    path.lineTo(size.x * 0.7, size.y * 0.6);
    path.lineTo(size.x * 0.5, size.y);

    canvas.drawPath(path, lightningPaint);

    // توهج حول البرق
    final glowPaint = Paint()
      ..color = Colors.yellow.withAlpha(100)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);
    canvas.drawPath(path, glowPaint);
  }

  void _drawFireBall(Canvas canvas) {
    // كرة النار الرئيسية
    final firePaint = Paint()
      ..shader = RadialGradient(
        colors: [Colors.yellow, Colors.orange, Colors.red],
      ).createShader(Rect.fromCircle(center: Offset(size.x * 0.5, size.y * 0.5), radius: size.x * 0.5));

    canvas.drawCircle(Offset(size.x * 0.5, size.y * 0.5), size.x * 0.5, firePaint);

    // شرارات حول كرة النار
    final sparkPaint = Paint()..color = Colors.orange;
    for (int i = 0; i < 4; i++) {
      final sparkX = size.x * 0.5 + sin(_effectFrame * 8 + i) * 8;
      final sparkY = size.y * 0.5 + cos(_effectFrame * 8 + i) * 8;
      canvas.drawCircle(Offset(sparkX, sparkY), 1, sparkPaint);
    }
  }

  void _drawMissileProjectile(Canvas canvas) {
    // جسم الصاروخ الصغير
    final missilePaint = Paint()..color = Colors.orange;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.x * 0.2, 0, size.x * 0.6, size.y * 0.8),
        const Radius.circular(2),
      ),
      missilePaint,
    );

    // رأس الصاروخ
    final nosePaint = Paint()..color = Colors.red;
    final nosePath = Path();
    nosePath.moveTo(size.x * 0.5, 0);
    nosePath.lineTo(size.x * 0.2, size.y * 0.2);
    nosePath.lineTo(size.x * 0.8, size.y * 0.2);
    nosePath.close();
    canvas.drawPath(nosePath, nosePaint);

    // اللهب الخلفي
    final flamePaint = Paint()..color = Colors.blue;
    canvas.drawOval(
      Rect.fromLTWH(size.x * 0.3, size.y * 0.8, size.x * 0.4, size.y * 0.2),
      flamePaint,
    );
  }
}