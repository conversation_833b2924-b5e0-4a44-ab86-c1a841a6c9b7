import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/collisions.dart';
import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;
import 'game_screen.dart';

// مدير الأصوات
class SoundManager {
  static bool _soundEnabled = true;
  static bool _musicEnabled = true;

  static Future<void> playSound(String soundType) async {
    if (!_soundEnabled) return;

    try {
      switch (soundType) {
        case 'water_flow':
        case 'dig':
        case 'splash':
          await SystemSound.play(SystemSoundType.click);
          HapticFeedback.lightImpact();
          break;
        case 'success':
        case 'level_complete':
          await SystemSound.play(SystemSoundType.click);
          await Future.delayed(const Duration(milliseconds: 100));
          await SystemSound.play(SystemSoundType.click);
          HapticFeedback.heavyImpact();
          break;
        case 'fail':
          await SystemSound.play(SystemSoundType.alert);
          HapticFeedback.heavyImpact();
          break;
        default:
          await SystemSound.play(SystemSoundType.click);
          HapticFeedback.lightImpact();
      }
    } catch (e) {
      print('خطأ في تشغيل الصوت: $e');
      HapticFeedback.lightImpact();
    }
  }

  static void toggleSound() {
    _soundEnabled = !_soundEnabled;
    if (_soundEnabled) {
      HapticFeedback.lightImpact();
    }
  }

  static void toggleMusic() {
    _musicEnabled = !_musicEnabled;
    if (_musicEnabled) {
      HapticFeedback.lightImpact();
    }
  }

  static bool get isSoundEnabled => _soundEnabled;
  static bool get isMusicEnabled => _musicEnabled;
}

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const ArzaFlowApp());
}

class ArzaFlowApp extends StatelessWidget {
  const ArzaFlowApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ArzaFlow - Physics Puzzle',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        fontFamily: 'Arial',
      ),
      home: const MainMenuScreen(),
    );
  }
}

// شاشة القائمة الرئيسية
class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen>
    with TickerProviderStateMixin {
  late AnimationController _titleController;
  late AnimationController _waterController;
  late Animation<double> _titleAnimation;
  late Animation<double> _waterAnimation;

  @override
  void initState() {
    super.initState();

    _titleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _waterController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _titleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _titleController,
      curve: Curves.elasticOut,
    ));

    _waterAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waterController,
      curve: Curves.easeInOut,
    ));

    _titleController.forward();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _waterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB), // Sky Blue
              Color(0xFF4682B4), // Steel Blue
              Color(0xFF1E90FF), // Dodge Blue
              Color(0xFF0066CC), // Deep Blue
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // عنوان اللعبة مع تأثير الماء
                ScaleTransition(
                  scale: _titleAnimation,
                  child: Column(
                    children: [
                      // شعار اللعبة
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFF00BFFF),
                              Color(0xFF1E90FF),
                              Color(0xFF0066CC),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.blue.withOpacity(0.5),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.water_drop,
                          size: 60,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // اسم اللعبة
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            Color(0xFF00BFFF),
                            Color(0xFF1E90FF),
                            Color(0xFF0066CC),
                          ],
                        ).createShader(bounds),
                        child: const Text(
                          'ARZA',
                          style: TextStyle(
                            fontSize: 60,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            Color(0xFF1E90FF),
                            Color(0xFF0066CC),
                            Color(0xFF003399),
                          ],
                        ).createShader(bounds),
                        child: const Text(
                          'FLOW',
                          style: TextStyle(
                            fontSize: 60,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),

                      // وصف اللعبة
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.blue.withOpacity(0.8),
                              Colors.cyan.withOpacity(0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          'Physics Water Puzzle',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 80),

                // أزرار القائمة
                Column(
                  children: [
                    _buildMenuButton(
                      'START GAME',
                      Icons.play_arrow_rounded,
                      [Colors.green.shade400, Colors.green.shade600],
                      () => _startGame(),
                    ),
                    const SizedBox(height: 20),
                    _buildMenuButton(
                      'LEVELS',
                      Icons.map_rounded,
                      [Colors.orange.shade400, Colors.orange.shade600],
                      () => _showLevels(),
                    ),
                    const SizedBox(height: 20),
                    _buildMenuButton(
                      'SETTINGS',
                      Icons.settings_rounded,
                      [Colors.purple.shade400, Colors.purple.shade600],
                      () => _showSettings(),
                    ),
                  ],
                ),

                const SizedBox(height: 60),

                // معلومات الإصدار
                FadeTransition(
                  opacity: _titleAnimation,
                  child: const Text(
                    'Version 1.0.0 - Physics Puzzle Game',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(
    String text,
    IconData icon,
    List<Color> gradientColors,
    VoidCallback onPressed,
  ) {
    return Container(
      width: 300,
      height: 65,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(35),
        gradient: LinearGradient(colors: gradientColors),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.4),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(35),
          onTap: () {
            HapticFeedback.lightImpact();
            SoundManager.playSound('click');
            onPressed();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 30,
              ),
              const SizedBox(width: 15),
              Text(
                text,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _startGame() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const GameScreen(levelIndex: 0),
      ),
    );
  }

  void _showLevels() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LevelSelectScreen(),
      ),
    );
  }

  void _showSettings() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Settings',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // إعداد الصوت
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Sound Effects',
                        style: TextStyle(fontSize: 18),
                      ),
                      Switch(
                        value: SoundManager.isSoundEnabled,
                        onChanged: (value) {
                          setState(() {
                            SoundManager.toggleSound();
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // إعداد الموسيقى
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Music',
                        style: TextStyle(fontSize: 18),
                      ),
                      Switch(
                        value: SoundManager.isMusicEnabled,
                        onChanged: (value) {
                          setState(() {
                            SoundManager.toggleMusic();
                          });
                        },
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Close',
                style: TextStyle(fontSize: 18),
              ),
            ),
          ],
        );
      },
    );
  }
}

// شاشة اختيار المستويات
class LevelSelectScreen extends StatelessWidget {
  const LevelSelectScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4682B4),
              Color(0xFF1E90FF),
              Color(0xFF0066CC),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // شريط العنوان
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(
                        Icons.arrow_back_rounded,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                    const Expanded(
                      child: Text(
                        'Select Level',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 50),
                  ],
                ),
              ),

              // شبكة المستويات
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 4,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio: 1,
                    ),
                    itemCount: 20, // 20 مستوى للبداية
                    itemBuilder: (context, index) {
                      return _buildLevelButton(context, index + 1);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLevelButton(BuildContext context, int levelNumber) {
    final bool isUnlocked = levelNumber <= 5; // أول 5 مستويات مفتوحة
    final bool isCompleted = levelNumber <= 3; // أول 3 مستويات مكتملة

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: isUnlocked
              ? isCompleted
                  ? [Colors.green.shade400, Colors.green.shade600]
                  : [Colors.blue.shade400, Colors.blue.shade600]
              : [Colors.grey.shade400, Colors.grey.shade600],
        ),
        boxShadow: [
          BoxShadow(
            color: (isUnlocked ? Colors.blue : Colors.grey).withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: isUnlocked
              ? () {
                  HapticFeedback.lightImpact();
                  SoundManager.playSound('click');
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => GameScreen(levelIndex: levelNumber - 1),
                    ),
                  );
                }
              : null,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (isCompleted)
                const Icon(
                  Icons.star,
                  color: Colors.yellow,
                  size: 20,
                ),
              Text(
                '$levelNumber',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: isUnlocked ? Colors.white : Colors.grey.shade300,
                ),
              ),
              if (!isUnlocked)
                const Icon(
                  Icons.lock,
                  color: Colors.white70,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }
}