import 'dart:math';
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../arza_rush_game.dart';

class Obstacle extends RectangleComponent with HasGameRef<ArzaRushGame> {
  
  // Obstacle properties
  static const double obstacleWidth = 40.0;
  static const double minHeight = 60.0;
  static const double maxHeight = 120.0;
  
  // Movement
  double _speed = 0.0;
  
  // Visual properties
  late Color _color;
  final Random _random = Random();
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Set random height
    final height = minHeight + _random.nextDouble() * (maxHeight - minHeight);
    size = Vector2(obstacleWidth, height);
    
    // Set random color (red variations)
    _color = Color.lerp(
      Colors.red,
      Colors.deepOrange,
      _random.nextDouble(),
    )!;
    paint.color = _color;
    
    // Add collision detection
    add(RectangleHitbox());
    
    // Set initial speed
    _speed = gameRef.gameSpeed;
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    // Update speed to match game speed
    _speed = gameRef.gameSpeed;
    
    // Move obstacle to the left
    position.x -= _speed * dt;
    
    // Remove obstacle when it goes off screen
    if (position.x + size.x < 0) {
      removeFromParent();
    }
  }
  
  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Add some visual details to make it look more interesting
    final detailPaint = Paint()
      ..color = _color.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    // Draw some lines for texture
    for (int i = 1; i < 4; i++) {
      final y = size.y * i / 4;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.x, y),
        detailPaint,
      );
    }
    
    // Draw vertical lines
    for (int i = 1; i < 3; i++) {
      final x = size.x * i / 3;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.y),
        detailPaint,
      );
    }
    
    // Add a warning triangle on top
    final trianglePaint = Paint()..color = Colors.yellow;
    final trianglePath = Path();
    trianglePath.moveTo(size.x * 0.5, -10);
    trianglePath.lineTo(size.x * 0.3, -2);
    trianglePath.lineTo(size.x * 0.7, -2);
    trianglePath.close();
    canvas.drawPath(trianglePath, trianglePaint);
    
    // Add exclamation mark in triangle
    final exclamationPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 1;
    canvas.drawLine(
      Offset(size.x * 0.5, -8),
      Offset(size.x * 0.5, -5),
      exclamationPaint,
    );
    canvas.drawCircle(
      Offset(size.x * 0.5, -3.5),
      0.5,
      exclamationPaint,
    );
  }
}
